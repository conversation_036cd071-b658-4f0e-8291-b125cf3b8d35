import type { 
  Card, 
  GodCard, 
  FactionCard, 
  EventCard, 
  MiracleCard, 
  CulturalDecreeCard,
  CardCost,
  FactionColor,
  CardAge
} from '../types/card'

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Helper function to parse cost string into CardCost object
function parseCost(costString: string): CardCost | string {
  if (costString === '无' || costString === '0') {
    return { gold: 0 }
  }
  
  // Handle special costs like "X（最少 3）"
  if (costString.includes('X') || costString.includes('（')) {
    return costString
  }
  
  const cost: CardCost = {}
  
  // Parse cost string like "2 金币 + 1 文化"
  const parts = costString.split('+').map(s => s.trim())
  
  for (const part of parts) {
    if (part.includes('金币')) {
      const match = part.match(/(\d+)\s*金币/)
      if (match) cost.gold = parseInt(match[1])
    } else if (part.includes('文化')) {
      const match = part.match(/(\d+)\s*文化/)
      if (match) cost.culture = parseInt(match[1])
    } else if (part.includes('信仰')) {
      const match = part.match(/(\d+)\s*信仰/)
      if (match) cost.faith = parseInt(match[1])
    }
  }
  
  return cost
}

// Helper function to map faction name to color
function getFactionColor(factionName: string): FactionColor {
  switch (factionName) {
    case '红色': return 'red'
    case '黄色': return 'yellow'
    case '绿色': return 'green'
    case '紫色': return 'purple'
    case '灰色': return 'gray'
    default: return 'gray'
  }
}

export function createGodCard(data: {
  name: string
  passive: string
  divine_power: string
  faith_cost: number | string
}): GodCard {
  const now = Date.now()
  return {
    id: generateId(),
    name: data.name,
    category: 'god',
    passive: data.passive,
    divinePower: data.divine_power,
    faithCost: data.faith_cost,
    createdAt: now,
    updatedAt: now
  }
}

export function createFactionCard(data: {
  name: string
  quantity?: number
  faction: string
  age: string
  cost: string
  leader_effect?: string
  permanent_effect?: string
}): FactionCard {
  const now = Date.now()
  
  // Extract military power from permanent effect if it exists
  let militaryPower: number | undefined
  if (data.permanent_effect) {
    const powerMatch = data.permanent_effect.match(/(\d+)\s*战力/)
    if (powerMatch) {
      militaryPower = parseInt(powerMatch[1])
    }
  }
  
  return {
    id: generateId(),
    name: data.name,
    category: 'faction',
    faction: getFactionColor(data.faction),
    age: data.age as CardAge,
    quantity: data.quantity,
    cost: parseCost(data.cost),
    leaderEffect: data.leader_effect === '无' ? undefined : data.leader_effect,
    permanentEffect: data.permanent_effect === '无' ? undefined : data.permanent_effect,
    militaryPower,
    createdAt: now,
    updatedAt: now
  }
}

export function createEventCard(data: {
  name: string
  effect: string
  note?: string
}): EventCard {
  const now = Date.now()
  return {
    id: generateId(),
    name: data.name,
    category: 'event',
    effect: data.effect,
    note: data.note,
    createdAt: now,
    updatedAt: now
  }
}

export function createMiracleCard(data: {
  name: string
  condition: string
  vp: number
  one_time_bonus?: string
  suitable_route?: string
}): MiracleCard {
  const now = Date.now()
  return {
    id: generateId(),
    name: data.name,
    category: 'miracle',
    condition: data.condition,
    vp: data.vp,
    oneTimeBonus: data.one_time_bonus,
    suitableRoute: data.suitable_route,
    createdAt: now,
    updatedAt: now
  }
}

export function createCulturalDecreeCard(data: {
  name: string
  effect: string
  type?: 'basic' | 'advanced'
}): CulturalDecreeCard {
  const now = Date.now()
  return {
    id: generateId(),
    name: data.name,
    category: 'cultural_decree',
    effect: data.effect,
    type: data.type,
    createdAt: now,
    updatedAt: now
  }
}

// Preset templates for quick card creation
export const CARD_TEMPLATES: Record<string, Partial<Card>> = {
  god: {
    category: 'god',
    name: '新神祇',
    passive: '【被动能力】描述被动能力效果',
    divinePower: '【神力】描述主动能力效果',
    faithCost: 5
  } as Partial<GodCard>,
  
  faction_red: {
    category: 'faction',
    name: '新军事单位',
    faction: 'red',
    age: 'age1',
    cost: { gold: 2 },
    leaderEffect: '【建造时】获得军事优势',
    permanentEffect: '2 战力',
    militaryPower: 2
  } as Partial<FactionCard>,
  
  faction_yellow: {
    category: 'faction',
    name: '新经济单位',
    faction: 'yellow',
    age: 'age1',
    cost: { gold: 1 },
    leaderEffect: '【建造时】获得 2 金币',
    permanentEffect: '【时代结束】获得 1 金币'
  } as Partial<FactionCard>,
  
  faction_green: {
    category: 'faction',
    name: '新文化单位',
    faction: 'green',
    age: 'age1',
    cost: { gold: 1 },
    leaderEffect: '【建造时】获得 1 文化',
    permanentEffect: '【时代结束】获得 1 文化'
  } as Partial<FactionCard>,
  
  faction_purple: {
    category: 'faction',
    name: '新宗教单位',
    faction: 'purple',
    age: 'age1',
    cost: { gold: 1 },
    leaderEffect: '【建造时】获得 1 信仰',
    permanentEffect: '你的神祇能力费用 -1'
  } as Partial<FactionCard>,
  
  faction_gray: {
    category: 'faction',
    name: '新建筑',
    faction: 'gray',
    age: 'age1',
    cost: { gold: 2 },
    permanentEffect: '放置在此列的卡牌费用 -1'
  } as Partial<FactionCard>,
  
  event: {
    category: 'event',
    name: '新事件',
    effect: '描述事件的影响',
    note: '设计备注'
  } as Partial<EventCard>,
  
  miracle: {
    category: 'miracle',
    name: '新奇迹',
    condition: '达成特定条件',
    vp: 6,
    oneTimeBonus: '立即获得奖励',
    suitableRoute: '适合的发展路线'
  } as Partial<MiracleCard>,
  
  cultural_decree: {
    category: 'cultural_decree',
    name: '新文化法令',
    effect: '游戏结束时的计分效果',
    type: 'basic'
  } as Partial<CulturalDecreeCard>
}

// Function to create a card from template
export function createCardFromTemplate(templateKey: string, overrides: Partial<Card> = {}): Card {
  const template = CARD_TEMPLATES[templateKey]
  if (!template) {
    throw new Error(`Template ${templateKey} not found`)
  }
  
  const now = Date.now()
  return {
    id: generateId(),
    createdAt: now,
    updatedAt: now,
    ...template,
    ...overrides
  } as Card
}