import initSqlJs from 'sql.js'
import type { Database } from 'sql.js'
import type { Deck, Card } from '../types/card'

class CardForgeDB {
  private db: Database | null = null
  private initialized = false

  async init(): Promise<void> {
    if (this.initialized) return

    try {
      // Initialize SQL.js
      const SQL = await initSqlJs({
        // Use the SQL.js wasm file from CDN
        locateFile: (file: string) => `https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/${file}`
      })

      // Try to load existing database from localStorage
      const savedDB = localStorage.getItem('cardforge-db')
      if (savedDB) {
        const data = new Uint8Array(JSON.parse(savedDB))
        this.db = new SQL.Database(data)
      } else {
        this.db = new SQL.Database()
        this.createTables()
      }

      this.initialized = true
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  private createTables(): void {
    if (!this.db) throw new Error('Database not initialized')

    // Create decks table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS decks (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        settings TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    `)

    // Create cards table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS cards (
        id TEXT PRIMARY KEY,
        deck_id TEXT NOT NULL,
        card_data TEXT NOT NULL,
        card_order INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (deck_id) REFERENCES decks (id) ON DELETE CASCADE
      )
    `)

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_cards_deck_id ON cards (deck_id);
      CREATE INDEX IF NOT EXISTS idx_cards_order ON cards (deck_id, card_order);
    `)

    this.saveToLocalStorage()
  }

  private saveToLocalStorage(): void {
    if (!this.db) return

    try {
      const data = this.db.export()
      localStorage.setItem('cardforge-db', JSON.stringify(Array.from(data)))
    } catch (error) {
      console.error('Failed to save database to localStorage:', error)
    }
  }

  async saveDeck(deck: Deck): Promise<void> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    try {
      this.db.run('BEGIN TRANSACTION')

      // Save/update deck
      const deckExists = this.db.exec(
        'SELECT id FROM decks WHERE id = ?', 
        [deck.id]
      )[0]

      if (deckExists) {
        this.db.run(
          'UPDATE decks SET name = ?, settings = ?, updated_at = ? WHERE id = ?',
          [deck.settings.name, JSON.stringify(deck.settings), deck.updatedAt, deck.id]
        )
      } else {
        this.db.run(
          'INSERT INTO decks (id, name, settings, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
          [deck.id, deck.settings.name, JSON.stringify(deck.settings), deck.createdAt, deck.updatedAt]
        )
      }

      // Delete existing cards for this deck
      this.db.run('DELETE FROM cards WHERE deck_id = ?', [deck.id])

      // Insert all cards
      deck.cards.forEach((card, index) => {
        this.db!.run(
          'INSERT INTO cards (id, deck_id, card_data, card_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
          [card.id, deck.id, JSON.stringify(card), index, card.createdAt, card.updatedAt]
        )
      })

      this.db.run('COMMIT')
      this.saveToLocalStorage()
    } catch (error) {
      this.db.run('ROLLBACK')
      console.error('Failed to save deck:', error)
      throw error
    }
  }

  async loadDeck(deckId: string): Promise<Deck | null> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    try {
      // Load deck info
      const deckResult = this.db.exec(
        'SELECT id, name, settings, created_at, updated_at FROM decks WHERE id = ?',
        [deckId]
      )[0]

      if (!deckResult) return null

      const deckRow = deckResult.values[0]
      const deckSettings = JSON.parse(deckRow[2] as string)

      // Load cards
      const cardsResult = this.db.exec(
        'SELECT card_data FROM cards WHERE deck_id = ? ORDER BY card_order',
        [deckId]
      )[0]

      const cards: Card[] = cardsResult 
        ? cardsResult.values.map(row => JSON.parse(row[0] as string))
        : []

      return {
        id: deckRow[0] as string,
        settings: deckSettings,
        cards,
        createdAt: deckRow[3] as number,
        updatedAt: deckRow[4] as number
      }
    } catch (error) {
      console.error('Failed to load deck:', error)
      return null
    }
  }

  async getAllDecks(): Promise<Array<{ id: string; name: string; cardCount: number; updatedAt: number }>> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    try {
      const result = this.db.exec(`
        SELECT 
          d.id, 
          d.name, 
          d.updated_at,
          COUNT(c.id) as card_count
        FROM decks d
        LEFT JOIN cards c ON d.id = c.deck_id
        GROUP BY d.id, d.name, d.updated_at
        ORDER BY d.updated_at DESC
      `)

      if (!result[0]) return []

      return result[0].values.map(row => ({
        id: row[0] as string,
        name: row[1] as string,
        updatedAt: row[2] as number,
        cardCount: row[3] as number
      }))
    } catch (error) {
      console.error('Failed to get all decks:', error)
      return []
    }
  }

  async deleteDeck(deckId: string): Promise<void> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    try {
      this.db.run('BEGIN TRANSACTION')
      this.db.run('DELETE FROM cards WHERE deck_id = ?', [deckId])
      this.db.run('DELETE FROM decks WHERE id = ?', [deckId])
      this.db.run('COMMIT')
      this.saveToLocalStorage()
    } catch (error) {
      this.db.run('ROLLBACK')
      console.error('Failed to delete deck:', error)
      throw error
    }
  }

  async getLastUsedDeck(): Promise<string | null> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    try {
      const result = this.db.exec(
        'SELECT id FROM decks ORDER BY updated_at DESC LIMIT 1'
      )[0]

      return result ? result.values[0][0] as string : null
    } catch (error) {
      console.error('Failed to get last used deck:', error)
      return null
    }
  }

  async exportDatabase(): Promise<Uint8Array> {
    if (!this.initialized) await this.init()
    if (!this.db) throw new Error('Database not initialized')

    return this.db.export()
  }

  async importDatabase(data: Uint8Array): Promise<void> {
    if (!this.initialized) await this.init()

    try {
      const SQL = await initSqlJs({
        locateFile: (file: string) => `https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/${file}`
      })

      this.db = new SQL.Database(data)
      this.saveToLocalStorage()
    } catch (error) {
      console.error('Failed to import database:', error)
      throw error
    }
  }
}

// Singleton instance
export const cardForgeDB = new CardForgeDB()