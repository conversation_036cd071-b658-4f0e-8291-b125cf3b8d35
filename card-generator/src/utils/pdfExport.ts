import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import JSZip from 'jszip'
import type { Deck, Card, CardDimensions, LegacyCard, GodCard, FactionCard, EventCard, MiracleCard, CulturalDecreeCard } from '../types/card'
import { CARD_DIMENSIONS } from '../types/card'

export interface ExportOptions {
  includeBleed: boolean
  includeCropMarks: boolean
  oneCardPerPage: boolean
}

export class PDFExporter {
  private deck: Deck
  private dimensions: CardDimensions
  private options: ExportOptions

  constructor(deck: Deck, options: Partial<ExportOptions> = {}) {
    this.deck = deck
    this.dimensions = CARD_DIMENSIONS[deck.settings.cardSize]
    this.options = {
      includeBleed: true,
      includeCropMarks: true,
      oneCardPerPage: true,
      ...options
    }
  }

  private getCardDimensions(card: Card | LegacyCard): CardDimensions {
    const baseDimensions = CARD_DIMENSIONS[this.deck.settings.cardSize]
    const cardOrientation = card.orientation || 'portrait'
    
    // Adjust dimensions for landscape orientation
    if (cardOrientation === 'landscape') {
      return {
        ...baseDimensions,
        width: baseDimensions.height,
        height: baseDimensions.width
      }
    }
    
    return baseDimensions
  }

  private mmToPt(mm: number): number {
    return mm * 2.834645669
  }

  private getPageDimensions() {
    const cardWidth = this.dimensions.width
    const cardHeight = this.dimensions.height
    const bleed = this.options.includeBleed ? this.dimensions.bleed : 0
    const cropMarkSpace = this.options.includeCropMarks ? 5 : 0

    return {
      width: this.mmToPt(cardWidth + (bleed * 2) + (cropMarkSpace * 2)),
      height: this.mmToPt(cardHeight + (bleed * 2) + (cropMarkSpace * 2))
    }
  }

  private drawCropMarks(pdf: jsPDF, x: number, y: number, width: number, height: number) {
    if (!this.options.includeCropMarks) return

    const markLength = this.mmToPt(3)
    const markOffset = this.mmToPt(2)

    pdf.setLineWidth(0.25)
    pdf.setDrawColor(0, 0, 0)

    // Top-left corner
    pdf.line(x - markOffset - markLength, y - markOffset, x - markOffset, y - markOffset)
    pdf.line(x - markOffset, y - markOffset - markLength, x - markOffset, y - markOffset)

    // Top-right corner
    pdf.line(x + width + markOffset, y - markOffset, x + width + markOffset + markLength, y - markOffset)
    pdf.line(x + width + markOffset, y - markOffset - markLength, x + width + markOffset, y - markOffset)

    // Bottom-left corner
    pdf.line(x - markOffset - markLength, y + height + markOffset, x - markOffset, y + height + markOffset)
    pdf.line(x - markOffset, y + height + markOffset, x - markOffset, y + height + markOffset + markLength)

    // Bottom-right corner
    pdf.line(x + width + markOffset, y + height + markOffset, x + width + markOffset + markLength, y + height + markOffset)
    pdf.line(x + width + markOffset, y + height + markOffset, x + width + markOffset, y + height + markOffset + markLength)
  }

  private async renderCardToCanvas(card: Card | LegacyCard): Promise<HTMLCanvasElement> {
    const cardDimensions = this.getCardDimensions(card)
    
    // Calculate pixel dimensions for high-quality rendering
    const pixelWidth = Math.round(cardDimensions.width * this.deck.settings.dpi / 25.4) // Convert mm to pixels at target DPI
    const pixelHeight = Math.round(cardDimensions.height * this.deck.settings.dpi / 25.4)
    
    // Create a temporary div to render the card
    const tempDiv = document.createElement('div')
    
    // Completely isolate from global CSS by resetting all inherited styles
    tempDiv.style.cssText = `
      position: absolute !important;
      left: -9999px !important;
      top: -9999px !important;
      width: ${pixelWidth}px !important;
      height: ${pixelHeight}px !important;
      all: initial !important;
      font-family: Arial, sans-serif !important;
    `
    
    document.body.appendChild(tempDiv)

    // Create card HTML with proper pixel dimensions
    const cardHtml = this.generateCardHtml(card, pixelWidth, pixelHeight)
    tempDiv.innerHTML = cardHtml

    try {
      const canvas = await html2canvas(tempDiv, {
        scale: 1, // Don't scale since we're already at target resolution
        width: pixelWidth,
        height: pixelHeight,
        backgroundColor: '#ffffff',
        useCORS: true,
        allowTaint: false,
        ignoreElements: (element) => {
          // Skip any elements that might have problematic CSS
          return element.tagName === 'STYLE' || element.tagName === 'LINK'
        }
      })

      document.body.removeChild(tempDiv)
      return canvas
    } catch (error) {
      document.body.removeChild(tempDiv)
      throw error
    }
  }

  private generateCardHtml(card: Card | LegacyCard, pixelWidth?: number, pixelHeight?: number): string {
    const cardDimensions = this.getCardDimensions(card)
    const cardWidth = pixelWidth || cardDimensions.width * 4
    const cardHeight = pixelHeight || cardDimensions.height * 4
    
    // Calculate scale factor for responsive sizing
    const scaleFactor = cardWidth / (cardDimensions.width * 4)
    
    // Check if it's a legacy card
    const isLegacy = 'title' in card
    const cardName = isLegacy ? (card as LegacyCard).title : (card as Card).name
    const description = isLegacy ? (card as LegacyCard).description : this.getCardDescription(card as Card)
    
    // Get card-specific styling for new cards
    const cardStyles = this.getCardStyling(card)
    
    // Scale dimensions
    const padding = Math.round(8 * scaleFactor)
    const headerFontSize = Math.round(32 * scaleFactor)
    const subtitleFontSize = Math.round(24 * scaleFactor)
    const contentFontSize = Math.round(24 * scaleFactor)
    const statFontSize = Math.round(20 * scaleFactor)
    const costFontSize = Math.round(32 * scaleFactor)
    const borderRadius = Math.round(4 * scaleFactor)
    const costSize = Math.round(64 * scaleFactor)
    
    return `
      <div style="
        all: initial !important;
        width: ${cardWidth}px !important;
        height: ${cardHeight}px !important;
        background: ${cardStyles.background} !important;
        border: ${Math.round(3 * scaleFactor)}px solid rgb(51, 51, 51) !important;
        position: relative !important;
        font-family: Arial, sans-serif !important;
        overflow: hidden !important;
        box-sizing: border-box !important;
        display: block !important;
      ">
        ${card.backgroundImage ? `
          <img src="${card.backgroundImage.url}" 
               style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; object-fit: cover !important;" 
               onerror="this.style.display='none'" />
        ` : ''}
        
        <!-- Header -->
        <div style="
          all: initial !important;
          background: ${cardStyles.headerBg} !important; 
          color: ${cardStyles.headerText} !important; 
          padding: ${padding}px !important; 
          text-align: center !important;
          border-bottom: ${Math.round(2 * scaleFactor)}px solid rgb(51, 51, 51) !important;
          display: block !important;
        ">
          <h3 style="
            all: initial !important;
            font-size: ${headerFontSize}px !important; 
            font-weight: bold !important; 
            margin: 0 !important;
            line-height: 1.2 !important;
            color: ${cardStyles.headerText} !important;
            font-family: Arial, sans-serif !important;
            display: block !important;
          ">${cardName || 'Untitled Card'}</h3>
          ${cardStyles.subtitle ? `
            <div style="all: initial !important; font-size: ${subtitleFontSize}px !important; margin-top: ${Math.round(4 * scaleFactor)}px !important; color: ${cardStyles.headerText} !important; font-family: Arial, sans-serif !important; display: block !important;">${cardStyles.subtitle}</div>
          ` : ''}
        </div>
        
        <!-- Illustration Area -->
        ${card.illustrationImage ? `
          <div style="
            all: initial !important;
            height: 50% !important; 
            margin: ${padding}px !important; 
            border: ${Math.round(2 * scaleFactor)}px solid rgb(204, 204, 204) !important; 
            border-radius: ${borderRadius}px !important;
            overflow: hidden !important;
            display: block !important;
          ">
            <img src="${card.illustrationImage.url}" 
                 style="all: initial !important; width: 100% !important; height: 100% !important; object-fit: cover !important; display: block !important;" 
                 onerror="this.style.display='none'" />
          </div>
        ` : ''}
        
        <!-- Content Area -->
        <div style="
          all: initial !important;
          position: absolute !important; 
          bottom: ${padding}px !important; 
          left: ${padding}px !important; 
          right: ${padding}px !important;
          background: rgba(255,255,255,0.95) !important; 
          padding: ${padding}px !important; 
          border-radius: ${borderRadius}px !important;
          border: ${Math.round(1 * scaleFactor)}px solid rgb(221, 221, 221) !important;
          display: block !important;
        ">
          ${description ? `
            <div style="
              all: initial !important;
              font-size: ${contentFontSize}px !important; 
              line-height: 1.3 !important;
              color: rgb(51, 51, 51) !important;
              font-family: Arial, sans-serif !important;
              display: block !important;
            ">
              ${description.replace(/\n/g, '<br>')}
            </div>
          ` : ''}
          
          ${!isLegacy && (card as Card).category === 'faction' && (card as FactionCard).militaryPower ? `
            <div style="
              all: initial !important;
              background: rgb(220, 38, 38) !important; 
              color: rgb(255, 255, 255) !important; 
              font-size: ${statFontSize}px !important; 
              padding: ${Math.round(4 * scaleFactor)}px ${padding}px !important; 
              border-radius: ${borderRadius}px !important;
              margin-top: ${Math.round(4 * scaleFactor)}px !important;
              display: inline-block !important;
              font-family: Arial, sans-serif !important;
            ">
              战力: ${(card as FactionCard).militaryPower}
            </div>
          ` : ''}
          
          ${isLegacy && (card as LegacyCard).stats && Object.keys((card as LegacyCard).stats).length > 0 ? `
            <div style="all: initial !important; margin-top: ${Math.round(4 * scaleFactor)}px !important; display: block !important;">
              ${Object.entries((card as LegacyCard).stats).map(([key, value]) => `
                <span style="
                  all: initial !important;
                  background: rgb(220, 38, 38) !important; 
                  color: rgb(255, 255, 255) !important; 
                  font-size: ${statFontSize}px !important; 
                  padding: ${Math.round(4 * scaleFactor)}px ${padding}px !important; 
                  border-radius: ${borderRadius}px !important;
                  margin-right: ${Math.round(4 * scaleFactor)}px !important;
                  display: inline-block !important;
                  font-family: Arial, sans-serif !important;
                ">
                  ${key}: ${value}
                </span>
              `).join('')}
            </div>
          ` : ''}
        </div>
        
        ${isLegacy && (card as LegacyCard).cost !== undefined ? `
          <div style="
            all: initial !important;
            position: absolute !important;
            top: ${padding}px !important;
            right: ${padding}px !important;
            background: rgb(251, 191, 36) !important; 
            color: rgb(0, 0, 0) !important; 
            font-weight: bold !important; 
            border-radius: 50% !important; 
            width: ${costSize}px !important; 
            height: ${costSize}px !important; 
            display: flex !important; 
            align-items: center !important; 
            justify-content: center !important; 
            font-size: ${costFontSize}px !important;
            border: ${Math.round(2 * scaleFactor)}px solid rgb(51, 51, 51) !important;
            font-family: Arial, sans-serif !important;
          ">
            ${(card as LegacyCard).cost}
          </div>
        ` : ''}
      </div>
    `
  }

  private getCardStyling(card: Card | LegacyCard) {
    // Default styling for legacy cards
    if ('title' in card) {
      return {
        background: 'rgb(255, 255, 255)',
        headerBg: 'rgb(107, 114, 128)',
        headerText: 'rgb(255, 255, 255)',
        subtitle: ''
      }
    }

    const gameCard = card as Card

    switch (gameCard.category) {
      case 'god':
        return {
          background: 'linear-gradient(to bottom, rgb(233, 213, 255), rgb(192, 132, 252))',
          headerBg: 'rgb(124, 58, 237)',
          headerText: 'rgb(255, 255, 255)',
          subtitle: `信仰: ${(gameCard as GodCard).faithCost}`
        }
      case 'faction':
        const factionCard = gameCard as FactionCard
        const factionColors = {
          red: { bg: 'rgb(254, 202, 202)', headerBg: 'rgb(220, 38, 38)', text: 'rgb(255, 255, 255)' },
          yellow: { bg: 'rgb(254, 243, 199)', headerBg: 'rgb(245, 158, 11)', text: 'rgb(0, 0, 0)' },
          green: { bg: 'rgb(220, 252, 231)', headerBg: 'rgb(22, 163, 74)', text: 'rgb(255, 255, 255)' },
          purple: { bg: 'rgb(233, 213, 255)', headerBg: 'rgb(147, 51, 234)', text: 'rgb(255, 255, 255)' },
          gray: { bg: 'rgb(243, 244, 246)', headerBg: 'rgb(107, 114, 128)', text: 'rgb(255, 255, 255)' }
        }
        const colorSet = factionColors[factionCard.faction] || factionColors.gray
        return {
          background: colorSet.bg,
          headerBg: colorSet.headerBg,
          headerText: colorSet.text,
          subtitle: `${factionCard.age.toUpperCase()} - ${this.getFactionName(factionCard.faction)}`
        }
      case 'event':
        return {
          background: 'linear-gradient(to bottom, rgb(254, 215, 170), rgb(251, 146, 60))',
          headerBg: 'rgb(234, 88, 12)',
          headerText: 'rgb(255, 255, 255)',
          subtitle: '事件卡'
        }
      case 'miracle':
        return {
          background: 'linear-gradient(to bottom, rgb(254, 243, 199), rgb(251, 191, 36))',
          headerBg: 'rgb(217, 119, 6)',
          headerText: 'rgb(0, 0, 0)',
          subtitle: `奇迹 - ${(gameCard as MiracleCard).vp} VP`
        }
      case 'cultural_decree':
        return {
          background: 'linear-gradient(to bottom, rgb(219, 234, 254), rgb(96, 165, 250))',
          headerBg: 'rgb(37, 99, 235)',
          headerText: 'rgb(255, 255, 255)',
          subtitle: `文化法令${(gameCard as CulturalDecreeCard).type ? ` (${(gameCard as CulturalDecreeCard).type})` : ''}`
        }
      default:
        return {
          background: 'rgb(255, 255, 255)',
          headerBg: 'rgb(107, 114, 128)',
          headerText: 'rgb(255, 255, 255)',
          subtitle: ''
        }
    }
  }

  private getFactionName(faction: string): string {
    const names = {
      red: '红色 (军事)',
      yellow: '黄色 (经济)',
      green: '绿色 (文化)',
      purple: '紫色 (信仰)',
      gray: '灰色 (建筑)'
    }
    return names[faction as keyof typeof names] || faction
  }

  private getCardDescription(card: Card): string {
    switch (card.category) {
      case 'god':
        return `【被动】${card.passive}\n【神力】${card.divinePower}`
      case 'faction':
        const parts = []
        if (card.leaderEffect) parts.push(`【领袖效果】${card.leaderEffect}`)
        if (card.permanentEffect) parts.push(`【常驻效果】${card.permanentEffect}`)
        return parts.join('\n')
      case 'event':
        return `【效果】${card.effect}${card.note ? `\n${card.note}` : ''}`
      case 'miracle':
        return `【条件】${card.condition}\n${card.vp} VP${card.oneTimeBonus ? `\n【奖励】${card.oneTimeBonus}` : ''}`
      case 'cultural_decree':
        return `【效果】${card.effect}`
      default:
        return ''
    }
  }

  async exportToPDF(): Promise<Blob[]> {
    console.log(`exportToPDF: Processing ${this.deck.cards.length} cards`)
    const pdfBlobs: Blob[] = []

    for (let i = 0; i < this.deck.cards.length; i++) {
      console.log(`Processing card ${i + 1}/${this.deck.cards.length}`)
      
      const card = this.deck.cards[i]
      const cardDimensions = this.getCardDimensions(card)
      const pageDimensions = this.getPageDimensionsForCard(cardDimensions)
      
      // Create individual PDF for each card
      const pdf = new jsPDF({
        orientation: pageDimensions.width > pageDimensions.height ? 'landscape' : 'portrait',
        unit: 'pt',
        format: [pageDimensions.width, pageDimensions.height]
      })

      const bleed = this.options.includeBleed ? this.mmToPt(cardDimensions.bleed) : 0
      const cropMarkSpace = this.options.includeCropMarks ? this.mmToPt(5) : 0
      const cardX = cropMarkSpace
      const cardY = cropMarkSpace
      const cardWidth = this.mmToPt(cardDimensions.width) + (bleed * 2)
      const cardHeight = this.mmToPt(cardDimensions.height) + (bleed * 2)

      try {
        // Render card to canvas
        console.log(`Rendering card ${i + 1} to canvas`)
        const canvas = await this.renderCardToCanvas(card)
        console.log(`Canvas created for card ${i + 1}: ${canvas.width}x${canvas.height}`)
        
        // Add card image to PDF - use full card dimensions
        const imgData = canvas.toDataURL('image/jpeg', 0.95)
        pdf.addImage(imgData, 'JPEG', cardX, cardY, cardWidth, cardHeight)
        
        // Add crop marks
        this.drawCropMarks(pdf, cardX + bleed, cardY + bleed, 
                          this.mmToPt(cardDimensions.width), 
                          this.mmToPt(cardDimensions.height))
        
        console.log(`Successfully processed card ${i + 1}`)
        
      } catch (error) {
        console.error(`Failed to render card ${i + 1}:`, error)
        
        // Add placeholder for failed card
        pdf.setFillColor(240, 240, 240)
        pdf.rect(cardX, cardY, cardWidth, cardHeight, 'F')
        pdf.setFontSize(12)
        pdf.setTextColor(100, 100, 100)
        pdf.text(`Card ${i + 1} - Render Error`, cardX + cardWidth/2, cardY + cardHeight/2, { align: 'center' })
      }

      // Add individual PDF to collection
      const blob = pdf.output('blob')
      pdfBlobs.push(blob)
      console.log(`Added PDF blob ${i + 1} to collection (size: ${blob.size} bytes)`)
    }

    console.log(`exportToPDF: Completed processing. Generated ${pdfBlobs.length} PDF blobs`)
    return pdfBlobs
  }

  private getPageDimensionsForCard(cardDimensions: CardDimensions) {
    const cardWidth = cardDimensions.width
    const cardHeight = cardDimensions.height
    const bleed = this.options.includeBleed ? cardDimensions.bleed : 0
    const cropMarkSpace = this.options.includeCropMarks ? 5 : 0

    return {
      width: this.mmToPt(cardWidth + (bleed * 2) + (cropMarkSpace * 2)),
      height: this.mmToPt(cardHeight + (bleed * 2) + (cropMarkSpace * 2))
    }
  }

  async downloadPDF(filename?: string): Promise<void> {
    console.log(`Starting PDF export for ${this.deck.cards.length} cards`)
    const pdfBlobs = await this.exportToPDF()
    console.log(`Generated ${pdfBlobs.length} PDF blobs`)
    
    // Create ZIP archive
    const zip = new JSZip()
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    const baseName = filename || this.deck.settings.name.replace(/\s+/g, '_')
    
    // Group cards by type
    const cardsByType: { [key: string]: { card: Card | LegacyCard; blob: Blob; index: number }[] } = {}
    
    for (let i = 0; i < this.deck.cards.length; i++) {
      const card = this.deck.cards[i]
      const blob = pdfBlobs[i]
      
      // Determine card type folder name
      let folderName = 'Unknown'
      if ('category' in card) {
        const gameCard = card as Card
        switch (gameCard.category) {
          case 'god':
            folderName = '01_Gods'
            break
          case 'faction':
            const factionCard = gameCard as FactionCard
            folderName = `02_Factions/${this.getFactionFolderName(factionCard.faction)}`
            break
          case 'event':
            folderName = '03_Events'
            break
          case 'miracle':
            folderName = '04_Miracles'
            break
          case 'cultural_decree':
            folderName = '05_Cultural_Decrees'
            break
        }
      } else {
        folderName = '06_Legacy_Cards'
      }
      
      if (!cardsByType[folderName]) {
        cardsByType[folderName] = []
      }
      cardsByType[folderName].push({ card, blob, index: i })
    }
    
    // Add files to ZIP with organized folder structure
    for (const [folderName, cards] of Object.entries(cardsByType)) {
      console.log(`Adding ${cards.length} cards to folder: ${folderName}`)
      
      for (let j = 0; j < cards.length; j++) {
        const { card, blob, index } = cards[j]
        const cardName = ('name' in card ? card.name : (card as any).title || 'Untitled')
          .replace(/[/\\?%*:|"<>]/g, '_') // Remove invalid filename characters
          .replace(/\s+/g, '_')
        
        const fileName = `${String(j + 1).padStart(2, '0')}_${cardName}.pdf`
        zip.file(`${folderName}/${fileName}`, blob)
      }
    }
    
    // Generate and download ZIP
    console.log('Generating ZIP archive...')
    const zipBlob = await zip.generateAsync({ type: 'blob' })
    const zipFileName = `${baseName}_${timestamp}.zip`
    
    const url = URL.createObjectURL(zipBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = zipFileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    console.log(`Downloaded ZIP archive: ${zipFileName} (${zipBlob.size} bytes)`)
  }
  
  private getFactionFolderName(faction: string): string {
    const names = {
      red: 'Red_Military',
      yellow: 'Yellow_Economic', 
      green: 'Green_Cultural',
      purple: 'Purple_Faith',
      gray: 'Gray_Building'
    }
    return names[faction as keyof typeof names] || `Unknown_${faction}`
  }
}