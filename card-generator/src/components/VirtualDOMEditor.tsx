import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Save, X, Plus, Trash2, Move } from "lucide-react";
import type { CustomCardType, CustomCardElement, CustomField, LayoutMethod } from "../types/customCard";

interface VirtualDOMEditorProps {
  cardType: CustomCardType;
  onChange: (cardType: CustomCardType) => void;
  onSave: (cardType: CustomCardType) => void;
  onCancel: () => void;
}

export function VirtualDOMEditor({ cardType, onChange, onSave, onCancel }: VirtualDOMEditorProps) {
  const [localCardType, setLocalCardType] = useState<CustomCardType>(cardType);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);

  const updateCardType = (updates: Partial<CustomCardType>) => {
    const updated = { ...localCardType, ...updates, updatedAt: Date.now() };
    setLocalCardType(updated);
    onChange(updated);
  };

  const selectedElement = localCardType.elements.find(el => el.id === selectedElementId);

  const addElement = (type: "container" | "field", parentId?: string) => {
    const newElement: CustomCardElement = {
      id: `element_${Date.now()}`,
      name: type === "container" ? "New Container" : "New Field",
      type,
      position: {
        x: 0,
        y: 0,
        width: 100,
        height: type === "container" ? 50 : 20,
        zIndex: localCardType.elements.length
      }
    };

    if (type === "container") {
      newElement.children = [];
      newElement.layoutMethod = "flexbox";
      newElement.flexboxStyle = {
        flexDirection: "column",
        justifyContent: "flex-start",
        alignItems: "stretch",
        flexWrap: "nowrap",
        gap: 8,
        padding: { top: 8, right: 8, bottom: 8, left: 8 }
      };
      newElement.backgroundColor = "#f8f9fa";
      newElement.borderRadius = 4;
    } else {
      // For field elements, we need to reference an existing field or create one
      if (localCardType.fields.length > 0) {
        newElement.fieldId = localCardType.fields[0].id;
      }
    }

    const updatedElements = [...localCardType.elements, newElement];

    // If adding to a parent container, update the parent's children
    if (parentId) {
      const parentElement = updatedElements.find(el => el.id === parentId);
      if (parentElement && parentElement.type === "container") {
        parentElement.children = [...(parentElement.children || []), newElement.id];
      }
    }

    updateCardType({ elements: updatedElements });
    setSelectedElementId(newElement.id);
  };

  const updateElement = (elementId: string, updates: Partial<CustomCardElement>) => {
    const updatedElements = localCardType.elements.map(element =>
      element.id === elementId ? { ...element, ...updates } : element
    );
    updateCardType({ elements: updatedElements });
  };

  const removeElement = (elementId: string) => {
    if (elementId === localCardType.rootElementId) {
      alert("Cannot remove the root element");
      return;
    }

    const updatedElements = localCardType.elements.filter(el => el.id !== elementId);
    
    // Remove from parent containers
    updatedElements.forEach(element => {
      if (element.type === "container" && element.children) {
        element.children = element.children.filter(childId => childId !== elementId);
      }
    });

    updateCardType({ elements: updatedElements });
    
    if (selectedElementId === elementId) {
      setSelectedElementId(null);
    }
  };

  const handleSave = () => {
    onSave(localCardType);
  };

  return (
    <div className="flex h-full gap-4">
      {/* Canvas Area */}
      <div className="flex-1 bg-gray-50 rounded-lg p-4">
        <div className="bg-white rounded-lg shadow-sm border-2 border-dashed border-gray-300 min-h-[400px] relative">
          <div className="absolute top-2 left-2 text-xs text-gray-500">
            Canvas ({localCardType.dimensions.width}×{localCardType.dimensions.height}mm)
          </div>
          
          {/* Render elements */}
          <div className="p-8 h-full">
            <ElementRenderer
              element={localCardType.elements.find(el => el.id === localCardType.rootElementId)!}
              elements={localCardType.elements}
              fields={localCardType.fields}
              selectedElementId={selectedElementId}
              onSelectElement={setSelectedElementId}
            />
          </div>
        </div>

        {/* Canvas Controls */}
        <div className="mt-4 flex gap-2">
          <Button onClick={() => addElement("container")} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Container
          </Button>
          <Button onClick={() => addElement("field")} size="sm" disabled={localCardType.fields.length === 0}>
            <Plus className="w-4 h-4 mr-2" />
            Add Field
          </Button>
          {localCardType.fields.length === 0 && (
            <span className="text-xs text-gray-500 self-center ml-2">
              Add fields in the Properties tab first
            </span>
          )}
        </div>
      </div>

      {/* Properties Panel */}
      <div className="w-80 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Element Properties</CardTitle>
            <CardDescription>
              {selectedElement ? `Editing: ${selectedElement.name}` : "Select an element to edit"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedElement ? (
              <ElementPropertiesEditor
                element={selectedElement}
                fields={localCardType.fields}
                onUpdate={(updates) => updateElement(selectedElement.id, updates)}
                onRemove={() => removeElement(selectedElement.id)}
                canRemove={selectedElement.id !== localCardType.rootElementId}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Click on an element in the canvas to edit its properties.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Element Tree */}
        <Card>
          <CardHeader>
            <CardTitle>Element Tree</CardTitle>
          </CardHeader>
          <CardContent>
            <ElementTree
              elements={localCardType.elements}
              rootElementId={localCardType.rootElementId}
              selectedElementId={selectedElementId}
              onSelectElement={setSelectedElementId}
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <Button onClick={handleSave} className="w-full">
            <Save className="w-4 h-4 mr-2" />
            Save Design
          </Button>
          <Button variant="outline" onClick={onCancel} className="w-full">
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}

interface ElementRendererProps {
  element: CustomCardElement;
  elements: CustomCardElement[];
  fields: CustomField[];
  selectedElementId: string | null;
  onSelectElement: (id: string) => void;
}

function ElementRenderer({ element, elements, fields, selectedElementId, onSelectElement }: ElementRendererProps) {
  const isSelected = element.id === selectedElementId;
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelectElement(element.id);
  };

  if (element.type === "container") {
    const children = element.children?.map(childId => 
      elements.find(el => el.id === childId)
    ).filter(Boolean) || [];

    return (
      <div
        onClick={handleClick}
        className={`border-2 rounded p-2 min-h-[40px] cursor-pointer transition-colors ${
          isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
        }`}
        style={{
          backgroundColor: element.backgroundColor,
          borderRadius: element.borderRadius
        }}
      >
        <div className="text-xs text-gray-600 mb-1">{element.name}</div>
        <div className="space-y-2">
          {children.map(child => (
            <ElementRenderer
              key={child.id}
              element={child}
              elements={elements}
              fields={fields}
              selectedElementId={selectedElementId}
              onSelectElement={onSelectElement}
            />
          ))}
        </div>
      </div>
    );
  } else {
    const field = fields.find(f => f.id === element.fieldId);
    
    return (
      <div
        onClick={handleClick}
        className={`border-2 rounded p-2 min-h-[30px] cursor-pointer transition-colors ${
          isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
        }`}
      >
        <div className="text-xs text-gray-600">
          {field ? `Field: ${field.name}` : "No field selected"}
        </div>
        <div className="text-sm text-gray-800">
          {field?.type === "text" ? "Sample Text" : field?.type === "image" ? "📷 Image" : "Field"}
        </div>
      </div>
    );
  }
}

interface ElementPropertiesEditorProps {
  element: CustomCardElement;
  fields: CustomField[];
  onUpdate: (updates: Partial<CustomCardElement>) => void;
  onRemove: () => void;
  canRemove: boolean;
}

function ElementPropertiesEditor({ element, fields, onUpdate, onRemove, canRemove }: ElementPropertiesEditorProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label>Element Name</Label>
        <Input
          value={element.name}
          onChange={(e) => onUpdate({ name: e.target.value })}
        />
      </div>

      {element.type === "field" && (
        <div>
          <Label>Linked Field</Label>
          <Select
            value={element.fieldId || ""}
            onValueChange={(value) => onUpdate({ fieldId: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a field" />
            </SelectTrigger>
            <SelectContent>
              {fields.map(field => (
                <SelectItem key={field.id} value={field.id}>
                  {field.name} ({field.type})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {element.type === "container" && (
        <div>
          <Label>Background Color</Label>
          <Input
            type="color"
            value={element.backgroundColor || "#ffffff"}
            onChange={(e) => onUpdate({ backgroundColor: e.target.value })}
          />
        </div>
      )}

      {canRemove && (
        <Button variant="destructive" onClick={onRemove} className="w-full">
          <Trash2 className="w-4 h-4 mr-2" />
          Remove Element
        </Button>
      )}
    </div>
  );
}

interface ElementTreeProps {
  elements: CustomCardElement[];
  rootElementId: string;
  selectedElementId: string | null;
  onSelectElement: (id: string) => void;
}

function ElementTree({ elements, rootElementId, selectedElementId, onSelectElement }: ElementTreeProps) {
  const renderTreeNode = (elementId: string, depth = 0) => {
    const element = elements.find(el => el.id === elementId);
    if (!element) return null;

    const isSelected = element.id === selectedElementId;

    return (
      <div key={element.id}>
        <div
          className={`flex items-center gap-2 p-1 rounded cursor-pointer text-sm ${
            isSelected ? "bg-blue-100 text-blue-800" : "hover:bg-gray-100"
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => onSelectElement(element.id)}
        >
          <Move className="w-3 h-3" />
          <span>{element.name}</span>
          <span className="text-xs text-gray-500">({element.type})</span>
        </div>
        {element.type === "container" && element.children?.map(childId => 
          renderTreeNode(childId, depth + 1)
        )}
      </div>
    );
  };

  return (
    <div className="space-y-1">
      {renderTreeNode(rootElementId)}
    </div>
  );
}
