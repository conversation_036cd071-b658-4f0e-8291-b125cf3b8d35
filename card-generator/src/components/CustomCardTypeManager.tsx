import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Edit, Trash2, Copy } from "lucide-react";
import { CustomCardTypeEditor } from "./CustomCardTypeEditor";
import { VirtualDOMEditor } from "./VirtualDOMEditor";
import type { CustomCardType } from "../types/customCard";
import { DEFAULT_CUSTOM_CARD_TYPE } from "../types/customCard";

interface CustomCardTypeManagerProps {
  onCreateCard?: (customCardTypeId: string) => void;
}

export function CustomCardTypeManager({ onCreateCard }: CustomCardTypeManagerProps) {
  const [customCardTypes, setCustomCardTypes] = useState<CustomCardType[]>([]);
  const [selectedCardType, setSelectedCardType] = useState<CustomCardType | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editorMode, setEditorMode] = useState<"create" | "edit">("create");

  // Load custom card types from localStorage (temporary storage)
  useEffect(() => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        setCustomCardTypes(JSON.parse(stored));
      } catch (error) {
        console.error("Failed to load custom card types:", error);
      }
    }
  }, []);

  // Save custom card types to localStorage
  const saveCustomCardTypes = (types: CustomCardType[]) => {
    localStorage.setItem("customCardTypes", JSON.stringify(types));
    setCustomCardTypes(types);
  };

  const handleCreateNew = () => {
    const newCardType: CustomCardType = {
      ...DEFAULT_CUSTOM_CARD_TYPE,
      id: `custom_${Date.now()}`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    setSelectedCardType(newCardType);
    setEditorMode("create");
    setIsEditorOpen(true);
  };

  const handleEdit = (cardType: CustomCardType) => {
    setSelectedCardType(cardType);
    setEditorMode("edit");
    setIsEditorOpen(true);
  };

  const handleDuplicate = (cardType: CustomCardType) => {
    const duplicated: CustomCardType = {
      ...cardType,
      id: `custom_${Date.now()}`,
      name: `${cardType.name} (Copy)`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    const updatedTypes = [...customCardTypes, duplicated];
    saveCustomCardTypes(updatedTypes);
  };

  const handleDelete = (cardTypeId: string) => {
    if (confirm("Are you sure you want to delete this custom card type?")) {
      const updatedTypes = customCardTypes.filter(ct => ct.id !== cardTypeId);
      saveCustomCardTypes(updatedTypes);
    }
  };

  const handleSave = (cardType: CustomCardType) => {
    let updatedTypes: CustomCardType[];
    
    if (editorMode === "create") {
      updatedTypes = [...customCardTypes, cardType];
    } else {
      updatedTypes = customCardTypes.map(ct => 
        ct.id === cardType.id ? cardType : ct
      );
    }
    
    saveCustomCardTypes(updatedTypes);
    setIsEditorOpen(false);
    setSelectedCardType(null);
  };

  const handleCancel = () => {
    setIsEditorOpen(false);
    setSelectedCardType(null);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Custom Card Types</h1>
          <p className="text-gray-600 mt-2">
            Create and manage your custom card types with configurable layouts and fields.
          </p>
        </div>
        <Button onClick={handleCreateNew} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create New Type
        </Button>
      </div>

      {/* Card Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {customCardTypes.map((cardType) => (
          <Card key={cardType.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex justify-between items-start">
                <span className="truncate">{cardType.name}</span>
                <div className="flex gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEdit(cardType)}
                    className="h-8 w-8"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDuplicate(cardType)}
                    className="h-8 w-8"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(cardType.id)}
                    className="h-8 w-8 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardTitle>
              {cardType.description && (
                <CardDescription>{cardType.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-600">
                <div>
                  <strong>Dimensions:</strong> {cardType.dimensions.width}×{cardType.dimensions.height}mm
                </div>
                <div>
                  <strong>Fields:</strong> {cardType.fields.length}
                </div>
                <div>
                  <strong>Elements:</strong> {cardType.elements.length}
                </div>
                <div>
                  <strong>Orientation:</strong> {cardType.orientation}
                </div>
              </div>
              
              {onCreateCard && (
                <Button
                  className="w-full mt-4"
                  onClick={() => onCreateCard(cardType.id)}
                >
                  Create Card
                </Button>
              )}
            </CardContent>
          </Card>
        ))}
        
        {customCardTypes.length === 0 && (
          <div className="col-span-full text-center py-12">
            <div className="text-gray-400 mb-4">
              <Plus className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium">No custom card types yet</h3>
              <p className="text-sm">Create your first custom card type to get started.</p>
            </div>
            <Button onClick={handleCreateNew} className="mt-4">
              Create Your First Type
            </Button>
          </div>
        )}
      </div>

      {/* Editor Dialog */}
      <Dialog open={isEditorOpen} onOpenChange={setIsEditorOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              {editorMode === "create" ? "Create" : "Edit"} Custom Card Type
            </DialogTitle>
            <DialogDescription>
              Configure your card type properties and design the layout using the visual editor.
            </DialogDescription>
          </DialogHeader>
          
          {selectedCardType && (
            <Tabs defaultValue="properties" className="flex-1 overflow-hidden">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="properties">Properties</TabsTrigger>
                <TabsTrigger value="design">Design</TabsTrigger>
              </TabsList>
              
              <TabsContent value="properties" className="mt-4 overflow-y-auto max-h-[60vh]">
                <CustomCardTypeEditor
                  cardType={selectedCardType}
                  onChange={setSelectedCardType}
                  onSave={handleSave}
                  onCancel={handleCancel}
                />
              </TabsContent>
              
              <TabsContent value="design" className="mt-4 overflow-hidden max-h-[60vh]">
                <VirtualDOMEditor
                  cardType={selectedCardType}
                  onChange={setSelectedCardType}
                  onSave={handleSave}
                  onCancel={handleCancel}
                />
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
