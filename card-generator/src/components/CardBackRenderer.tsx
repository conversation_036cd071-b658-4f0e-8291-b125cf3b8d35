import type { Card, CardDimensions, CardBackImages } from "../types/card";

interface CardBackRendererProps {
  card: Card;
  dimensions: CardDimensions;
  backImages: CardBackImages;
  scale?: number;
  showBleed?: boolean;
  className?: string;
}

export function CardBackRenderer({
  card,
  dimensions,
  backImages,
  scale = 1,
  showBleed = false,
  className = "",
}: CardBackRendererProps) {
  const actualWidth = showBleed
    ? dimensions.width + dimensions.bleed * 2
    : dimensions.width;
  const actualHeight = showBleed
    ? dimensions.height + dimensions.bleed * 2
    : dimensions.height;

  const cardStyle = {
    width: `${actualWidth * scale}px`,
    height: `${actualHeight * scale}px`,
  };

  // Calculate content padding to respect bleed area
  const contentPadding = showBleed ? dimensions.bleed * scale : 2 * scale;

  // Base font sizes (in pixels relative to card width)
  const baseFontSizes = {
    title: dimensions.width * 0.08, // Larger for back design
    subtitle: dimensions.width * 0.05,
    body: dimensions.width * 0.04,
    small: dimensions.width * 0.035,
  };

  const titleFontSize = baseFontSizes.title * scale;
  const padding = Math.max(2, 4 * scale);

  // Get the back image for this card type
  const cardBackImage = backImages?.[card.category];

  // Get card type specific styling
  const getCardTypeStyle = () => {
    switch (card.category) {
      case "god":
        return {
          gradient:
            "bg-gradient-to-br from-purple-900 via-indigo-900 to-purple-800",
          title: "God Cards",
          subtitle: "Divine Powers",
        };
      case "faction":
        return {
          gradient:
            "bg-gradient-to-br from-slate-800 via-gray-900 to-slate-700",
          title: "Faction Cards",
          subtitle: "Civilization Building",
        };
      case "event":
        return {
          gradient:
            "bg-gradient-to-br from-orange-900 via-red-900 to-orange-800",
          title: "Event Cards",
          subtitle: "World Events",
        };
      case "miracle":
        return {
          gradient:
            "bg-gradient-to-br from-yellow-800 via-amber-900 to-yellow-700",
          title: "Miracle Cards",
          subtitle: "Victory Points",
        };
      case "cultural_decree":
        return {
          gradient: "bg-gradient-to-br from-blue-900 via-cyan-900 to-blue-800",
          title: "Cultural Decrees",
          subtitle: "Civilization Rules",
        };
      default:
        return {
          gradient: "bg-gradient-to-br from-gray-900 via-slate-900 to-gray-800",
          title: "God is With Me",
          subtitle: "Trading Card Game",
        };
    }
  };

  const cardTypeStyle = getCardTypeStyle();

  return (
    <div
      className={`relative border border-gray-800 overflow-hidden ${cardTypeStyle.gradient} ${className}`}
      style={cardStyle}
    >
      {/* Back Image */}
      {cardBackImage && (
        <div className="absolute inset-0">
          <img
            src={cardBackImage.url}
            alt={`${card.category} card back`}
            className="w-full h-full object-cover"
          />
          {cardBackImage.fallback && (
            <img
              src={cardBackImage.fallback}
              alt={`${card.category} card back fallback`}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = "none";
              }}
            />
          )}
        </div>
      )}

      {/* Default back design if no back image */}
      {!cardBackImage && (
        <div
          className="absolute flex items-center justify-center text-white"
          style={{
            left: `${contentPadding}px`,
            top: `${contentPadding}px`,
            right: `${contentPadding}px`,
            bottom: `${contentPadding}px`,
            padding: `${padding}px`,
          }}
        >
          <div className="text-center">
            <div
              className="font-bold text-white/90 mb-2"
              style={{ fontSize: `${titleFontSize}px`, lineHeight: 1.2 }}
            >
              {cardTypeStyle.title}
            </div>
            <div
              className="text-white/70"
              style={{ fontSize: `${baseFontSizes.body * scale}px` }}
            >
              {cardTypeStyle.subtitle}
            </div>
          </div>
        </div>
      )}

      {/* Bleed indicators */}
      {showBleed && (
        <>
          <div className="absolute inset-0 border-2 border-red-500 pointer-events-none opacity-50" />
          <div
            className="absolute border-2 border-blue-500 pointer-events-none opacity-50"
            style={{
              left: `${dimensions.bleed * scale}px`,
              top: `${dimensions.bleed * scale}px`,
              right: `${dimensions.bleed * scale}px`,
              bottom: `${dimensions.bleed * scale}px`,
            }}
          />
        </>
      )}
    </div>
  );
}
