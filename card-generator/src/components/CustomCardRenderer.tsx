import { useState } from "react";
import type { CustomCard, CustomCardType, CustomCardElement, CustomField, CustomCardData } from "../types/customCard";

interface CustomCardRendererProps {
  card: CustomCard;
  cardType: CustomCardType;
  scale?: number;
  showBleed?: boolean;
  className?: string;
}

interface ImageLoadState {
  [key: string]: "loading" | "loaded" | "error";
}

export function CustomCardRenderer({ 
  card, 
  cardType, 
  scale = 1, 
  showBleed = false, 
  className = "" 
}: CustomCardRendererProps) {
  const [imageLoadStates, setImageLoadStates] = useState<ImageLoadState>({});

  const { dimensions } = cardType;
  const bleedSize = showBleed ? dimensions.bleed : 0;
  
  // Calculate dimensions in pixels (assuming 96 DPI for screen display)
  const mmToPx = (mm: number) => (mm * 96) / 25.4;
  
  const cardWidth = mmToPx(dimensions.width + bleedSize * 2) * scale;
  const cardHeight = mmToPx(dimensions.height + bleedSize * 2) * scale;

  const handleImageLoad = (imageId: string) => {
    setImageLoadStates(prev => ({ ...prev, [imageId]: "loaded" }));
  };

  const handleImageError = (imageId: string) => {
    setImageLoadStates(prev => ({ ...prev, [imageId]: "error" }));
  };

  const getFieldValue = (fieldPath: string, data: CustomCardData): any => {
    const keys = fieldPath.split('.');
    let value = data;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return undefined;
      }
    }
    
    return value;
  };

  const rootElement = cardType.elements.find(el => el.id === cardType.rootElementId);
  if (!rootElement) {
    return (
      <div 
        className={`bg-red-100 border-2 border-red-300 flex items-center justify-center ${className}`}
        style={{ width: cardWidth, height: cardHeight }}
      >
        <span className="text-red-600 text-sm">Invalid card structure</span>
      </div>
    );
  }

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ 
        width: cardWidth, 
        height: cardHeight,
        fontSize: `${12 * scale}px`
      }}
    >
      {showBleed && (
        <div 
          className="absolute inset-0 border border-red-300 pointer-events-none"
          style={{
            margin: `${bleedSize * scale}px`
          }}
        />
      )}
      
      <ElementRenderer
        element={rootElement}
        elements={cardType.elements}
        fields={cardType.fields}
        cardData={card.data}
        scale={scale}
        onImageLoad={handleImageLoad}
        onImageError={handleImageError}
        imageLoadStates={imageLoadStates}
      />
    </div>
  );
}

interface ElementRendererProps {
  element: CustomCardElement;
  elements: CustomCardElement[];
  fields: CustomField[];
  cardData: CustomCardData;
  scale: number;
  onImageLoad: (imageId: string) => void;
  onImageError: (imageId: string) => void;
  imageLoadStates: ImageLoadState;
}

function ElementRenderer({
  element,
  elements,
  fields,
  cardData,
  scale,
  onImageLoad,
  onImageError,
  imageLoadStates
}: ElementRendererProps) {
  
  if (element.type === "container") {
    const children = element.children?.map(childId => 
      elements.find(el => el.id === childId)
    ).filter(Boolean) || [];

    const containerStyle: React.CSSProperties = {
      backgroundColor: element.backgroundColor,
      borderColor: element.borderColor,
      borderWidth: element.borderWidth ? `${element.borderWidth * scale}px` : undefined,
      borderRadius: element.borderRadius ? `${element.borderRadius * scale}px` : undefined,
      boxShadow: element.boxShadow,
      width: "100%",
      height: "100%"
    };

    if (element.layoutMethod === "flexbox" && element.flexboxStyle) {
      const flex = element.flexboxStyle;
      Object.assign(containerStyle, {
        display: "flex",
        flexDirection: flex.flexDirection,
        justifyContent: flex.justifyContent,
        alignItems: flex.alignItems,
        flexWrap: flex.flexWrap,
        gap: `${flex.gap * scale}px`,
        padding: `${flex.padding.top * scale}px ${flex.padding.right * scale}px ${flex.padding.bottom * scale}px ${flex.padding.left * scale}px`
      });
    } else if (element.layoutMethod === "absolute") {
      containerStyle.position = "relative";
    }

    return (
      <div style={containerStyle}>
        {children.map(child => (
          <ElementRenderer
            key={child.id}
            element={child}
            elements={elements}
            fields={fields}
            cardData={cardData}
            scale={scale}
            onImageLoad={onImageLoad}
            onImageError={onImageError}
            imageLoadStates={imageLoadStates}
          />
        ))}
      </div>
    );
  } else {
    // Field element
    const field = fields.find(f => f.id === element.fieldId);
    if (!field) {
      return (
        <div className="bg-gray-200 border border-gray-400 p-1 text-xs text-gray-600">
          Field not found
        </div>
      );
    }

    const fieldValue = getFieldValue(field.fieldPath, cardData);
    const elementStyle: React.CSSProperties = {};

    if (element.position && field.layoutMethod === "absolute") {
      Object.assign(elementStyle, {
        position: "absolute",
        left: `${element.position.x * scale}px`,
        top: `${element.position.y * scale}px`,
        width: `${element.position.width * scale}px`,
        height: `${element.position.height * scale}px`,
        zIndex: element.position.zIndex
      });
    }

    if (field.type === "text") {
      const textStyle = field.textStyle;
      if (textStyle) {
        Object.assign(elementStyle, {
          fontSize: `${textStyle.fontSize * scale}px`,
          fontFamily: textStyle.fontFamily,
          fontWeight: textStyle.fontWeight,
          color: textStyle.color,
          textAlign: textStyle.textAlign,
          lineHeight: textStyle.lineHeight,
          letterSpacing: `${textStyle.letterSpacing * scale}px`
        });
      }

      const displayValue = fieldValue !== undefined ? String(fieldValue) : (field.placeholder || field.name);

      return (
        <div style={elementStyle}>
          {displayValue}
        </div>
      );
    } else if (field.type === "image") {
      const imageStyle = field.imageStyle;
      const imageUrl = fieldValue?.url || fieldValue;
      
      if (!imageUrl) {
        return (
          <div 
            style={elementStyle}
            className="bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center text-gray-500"
          >
            <span className="text-xs">No image</span>
          </div>
        );
      }

      const imgStyle: React.CSSProperties = {
        width: "100%",
        height: "100%",
        objectFit: imageStyle?.objectFit || "cover",
        borderRadius: imageStyle?.borderRadius ? `${imageStyle.borderRadius * scale}px` : undefined,
        opacity: imageStyle?.opacity !== undefined ? imageStyle.opacity : 1
      };

      const imageId = `${element.id}_${field.id}`;
      const loadState = imageLoadStates[imageId];

      return (
        <div style={elementStyle} className="overflow-hidden">
          {loadState === "error" ? (
            <div className="w-full h-full bg-red-100 border border-red-300 flex items-center justify-center text-red-600 text-xs">
              Failed to load
            </div>
          ) : (
            <img
              src={imageUrl}
              alt={field.name}
              style={imgStyle}
              onLoad={() => onImageLoad(imageId)}
              onError={() => onImageError(imageId)}
              className={loadState === "loading" ? "opacity-50" : ""}
            />
          )}
        </div>
      );
    }

    return null;
  }
}
