import { useState } from "react";
import type {
  Card,
  CardDimensions,
  GodCard,
  FactionCard,
  EventCard,
  MiracleCard,
  CulturalDecreeCard,
  CardCost,
} from "../types/card";
import { FACTION_COLORS } from "../types/card";

interface GameCardRendererProps {
  card: Card;
  dimensions: CardDimensions;
  scale?: number;
  showBleed?: boolean;
  className?: string;
}

interface ImageLoadState {
  [key: string]: "loading" | "loaded" | "error";
}

export function GameCardRenderer({
  card,
  dimensions,
  scale = 1,
  showBleed = false,
  className = "",
}: GameCardRendererProps) {
  const [imageStates, setImageStates] = useState<ImageLoadState>({});

  const handleImageLoad = (key: string) => {
    setImageStates((prev) => ({ ...prev, [key]: "loaded" }));
  };

  const handleImageError = (key: string) => {
    setImageStates((prev) => ({ ...prev, [key]: "error" }));
  };

  const handleImageStart = (key: string) => {
    setImageStates((prev) => ({ ...prev, [key]: "loading" }));
  };

  const actualWidth = showBleed
    ? dimensions.width + dimensions.bleed * 2
    : dimensions.width;
  const actualHeight = showBleed
    ? dimensions.height + dimensions.bleed * 2
    : dimensions.height;

  // Base font sizes (in pixels relative to card width)
  const baseFontSizes = {
    title: dimensions.width * 0.06, // ~3.8px for 63mm card
    subtitle: dimensions.width * 0.04, // ~2.5px for 63mm card
    body: dimensions.width * 0.035, // ~2.2px for 63mm card
    small: dimensions.width * 0.03, // ~1.9px for 63mm card
  };

  const cardStyle = {
    width: `${actualWidth * scale}px`,
    height: `${actualHeight * scale}px`,
  };

  // Calculate content padding to respect bleed area
  const contentPadding = showBleed ? dimensions.bleed * scale : 2 * scale;

  const renderCardByType = () => {
    const commonProps = { scale, baseFontSizes };

    switch (card.category) {
      case "god":
        return <GodCardContent card={card as GodCard} {...commonProps} />;
      case "faction":
        return (
          <FactionCardContent card={card as FactionCard} {...commonProps} />
        );
      case "event":
        return <EventCardContent card={card as EventCard} {...commonProps} />;
      case "miracle":
        return (
          <MiracleCardContent card={card as MiracleCard} {...commonProps} />
        );
      case "cultural_decree":
        return (
          <CulturalDecreeCardContent
            card={card as CulturalDecreeCard}
            {...commonProps}
          />
        );
      default:
        return (
          <div className="flex items-center justify-center h-full text-gray-500">
            Unknown card type
          </div>
        );
    }
  };

  const getCardBackground = () => {
    if (card.category === "faction") {
      return "bg-white"; // Changed to white background for faction cards
    }

    switch (card.category) {
      case "god":
        return "bg-gradient-to-b from-purple-200 to-purple-400";
      case "event":
        return "bg-gradient-to-b from-orange-200 to-orange-400";
      case "miracle":
        return "bg-gradient-to-b from-yellow-200 to-yellow-400";
      case "cultural_decree":
        return "bg-gradient-to-b from-blue-200 to-blue-400";
      default:
        return "bg-white";
    }
  };

  return (
    <div
      className={`relative border border-gray-800 overflow-hidden ${getCardBackground()} ${className}`}
      style={cardStyle}
    >
      {/* Card Content - positioned to avoid bleed area */}
      <div
        className="absolute"
        style={{
          left: `${contentPadding}px`,
          top: `${contentPadding}px`,
          right: `${contentPadding}px`,
          bottom: `${contentPadding}px`,
          padding: `${2 * scale}px`,
        }}
      >
        {renderCardByType()}
      </div>

      {/* Bleed indicators */}
      {showBleed && (
        <>
          <div className="absolute inset-0 border-2 border-red-500 pointer-events-none opacity-50" />
          <div
            className="absolute border-2 border-blue-500 pointer-events-none opacity-50"
            style={{
              left: `${dimensions.bleed * scale}px`,
              top: `${dimensions.bleed * scale}px`,
              right: `${dimensions.bleed * scale}px`,
              bottom: `${dimensions.bleed * scale}px`,
            }}
          />
        </>
      )}
    </div>
  );
}

function GodCardContent({
  card,
  scale,
  baseFontSizes,
}: {
  card: GodCard;
  scale: number;
  baseFontSizes: {
    title: number;
    subtitle: number;
    body: number;
    small: number;
  };
}) {
  const titleFontSize = baseFontSizes.title * scale;
  const subtitleFontSize = baseFontSizes.subtitle * scale;
  const bodyFontSize = baseFontSizes.body * scale;
  const padding = Math.max(1, 2 * scale);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="bg-purple-800 text-white rounded-t text-center"
        style={{ padding: `${padding}px` }}
      >
        <h3
          className="font-bold leading-tight"
          style={{ fontSize: `${titleFontSize}px` }}
        >
          {card.name}
        </h3>
        <div style={{ fontSize: `${subtitleFontSize}px` }}>
          信仰: {card.faithCost}
        </div>
      </div>

      {/* Illustration area */}
      {card.illustrationImage && (
        <div
          className="flex-1 relative rounded overflow-hidden"
          style={{ margin: `${padding}px 0` }}
        >
          <img
            src={card.illustrationImage.url}
            alt="God illustration"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Abilities */}
      <div
        className="bg-white/90 rounded"
        style={{
          padding: `${padding}px`,
          fontSize: `${bodyFontSize}px`,
          lineHeight: 1.2,
        }}
      >
        <div style={{ marginBottom: `${padding / 2}px` }}>
          <span className="font-bold text-purple-700">【被动】</span>
          <p
            className="leading-tight"
            style={{ marginTop: `${padding / 4}px` }}
          >
            {card.passive}
          </p>
        </div>
        <div>
          <span className="font-bold text-purple-700">【神力】</span>
          <p
            className="leading-tight"
            style={{ marginTop: `${padding / 4}px` }}
          >
            {card.divinePower}
          </p>
        </div>
      </div>
    </div>
  );
}

function FactionCardContent({
  card,
  scale,
  baseFontSizes,
}: {
  card: FactionCard;
  scale: number;
  baseFontSizes: {
    title: number;
    subtitle: number;
    body: number;
    small: number;
  };
}) {
  const factionStyle = FACTION_COLORS[card.faction];
  const titleFontSize = baseFontSizes.title * scale;
  const bodyFontSize = baseFontSizes.body * scale;
  const smallFontSize = baseFontSizes.small * scale;
  const padding = Math.max(1, 2 * scale);
  const iconSize = Math.max(10, 7 * scale); // Made slightly bigger

  const renderCostIcons = (cost: CardCost | string) => {
    if (typeof cost === "string") {
      return null;
    }

    const costIcons = [];
    if (cost.gold && cost.gold > 0) {
      costIcons.push({
        type: "gold",
        value: cost.gold,
        color: "bg-orange-500",
        textColor: "text-white",
      });
    }
    if (cost.culture && cost.culture > 0) {
      costIcons.push({
        type: "culture",
        value: cost.culture,
        color: "bg-green-500",
        textColor: "text-white",
      });
    }
    if (cost.faith && cost.faith > 0) {
      costIcons.push({
        type: "faith",
        value: cost.faith,
        color: "bg-purple-500",
        textColor: "text-white",
      });
    }

    return costIcons.map((costIcon) => (
      <div
        key={costIcon.type}
        className={`${costIcon.color} ${costIcon.textColor} rounded-full flex items-center justify-center font-bold border-2 border-white shadow-md`}
        style={{
          width: `${iconSize}px`,
          height: `${iconSize}px`,
          fontSize: `${smallFontSize}px`,
          marginBottom: `${padding / 2}px`,
        }}
      >
        {costIcon.value}
      </div>
    ));
  };

  const hasCost = (cost: CardCost | string | undefined) => {
    if (!cost) return false;
    if (typeof cost === "string") return true;

    // Check if any cost value is greater than 0
    return (
      (cost.gold && cost.gold > 0) ||
      (cost.culture && cost.culture > 0) ||
      (cost.faith && cost.faith > 0) ||
      (cost.special && cost.special.trim().length > 0)
    );
  };

  return (
    <div className="h-full flex flex-col relative">
      {/* Header - Faction color bar with title */}
      <div
        className={`${factionStyle.bg} ${factionStyle.text} text-center font-bold`}
        style={{
          padding: `${padding * 1.5}px`,
          fontSize: `${titleFontSize}px`,
        }}
      >
        {card.name || "TITLE"}
      </div>

      {/* Main content area with cost icons and illustration space */}
      <div className="flex-1 relative">
        {/* Cost icons on the left */}
        {hasCost(card.cost) && (
          <div
            className="absolute left-0 top-0 z-20 flex flex-col"
            style={{
              padding: `${padding}px`,
              paddingTop: `${padding * 2}px`,
            }}
          >
            {renderCostIcons(card.cost)}
          </div>
        )}

        {/* Invisible div to preserve illustration space */}
        <div className="w-full h-full relative bg-gray-100">
          {/* Actual illustration image positioned absolutely underneath */}
          {card.illustrationImage && (
            <img
              src={card.illustrationImage.url}
              alt="Card illustration"
              className="absolute inset-0 w-full h-full object-cover z-10"
            />
          )}
        </div>
      </div>

      {/* Leader Effect Section */}
      {card.leaderEffect && (
        <div
          className="bg-white/90 text-center"
          style={{
            padding: `${padding}px`,
            fontSize: `${bodyFontSize}px`,
            lineHeight: 1.2,
          }}
        >
          <div className="font-bold text-gray-800">领神能力</div>
          <div className="text-gray-700 mt-1">{card.leaderEffect}</div>
        </div>
      )}

      {/* Permanent Effect Section */}
      {card.permanentEffect && (
        <div
          className="bg-white/90 text-center"
          style={{
            padding: `${padding}px`,
            fontSize: `${bodyFontSize}px`,
            lineHeight: 1.2,
          }}
        >
          <div className="font-bold text-gray-800">常驻能力</div>
          <div className="text-gray-700 mt-1">{card.permanentEffect}</div>
        </div>
      )}

      {/* Military Power Circle - Bottom right */}
      {card.militaryPower && card.militaryPower > 0 && (
        <div
          className="absolute bottom-0 right-0 bg-red-500 text-white rounded-full flex items-center justify-center font-bold border-2 border-white shadow-lg"
          style={{
            width: `${iconSize * 1.2}px`,
            height: `${iconSize * 1.2}px`,
            fontSize: `${bodyFontSize}px`,
            margin: `${padding}px`,
          }}
        >
          {card.militaryPower}
        </div>
      )}
    </div>
  );
}

function EventCardContent({
  card,
  scale,
  baseFontSizes,
}: {
  card: EventCard;
  scale: number;
  baseFontSizes: {
    title: number;
    subtitle: number;
    body: number;
    small: number;
  };
}) {
  const titleFontSize = baseFontSizes.title * scale;
  const subtitleFontSize = baseFontSizes.subtitle * scale;
  const bodyFontSize = baseFontSizes.body * scale;
  const padding = Math.max(1, 2 * scale);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="bg-orange-800 text-white rounded-t text-center"
        style={{ padding: `${padding}px` }}
      >
        <h3
          className="font-bold leading-tight"
          style={{ fontSize: `${titleFontSize}px` }}
        >
          {card.name}
        </h3>
        <div style={{ fontSize: `${subtitleFontSize}px` }}>事件卡</div>
      </div>

      {/* Illustration area */}
      {card.illustrationImage && (
        <div
          className="flex-1 relative rounded overflow-hidden"
          style={{ margin: `${padding}px 0` }}
        >
          <img
            src={card.illustrationImage.url}
            alt="Event illustration"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Effect */}
      <div
        className="bg-white/90 rounded"
        style={{
          padding: `${padding}px`,
          fontSize: `${bodyFontSize}px`,
          lineHeight: 1.2,
        }}
      >
        <div style={{ marginBottom: `${padding / 2}px` }}>
          <span className="font-bold text-orange-700">【效果】</span>
          <p
            className="leading-tight"
            style={{ marginTop: `${padding / 4}px` }}
          >
            {card.effect}
          </p>
        </div>
        {card.note && (
          <div className="text-gray-600 italic">
            <p className="leading-tight">{card.note}</p>
          </div>
        )}
      </div>
    </div>
  );
}

function MiracleCardContent({
  card,
  scale,
  baseFontSizes,
}: {
  card: MiracleCard;
  scale: number;
  baseFontSizes: {
    title: number;
    subtitle: number;
    body: number;
    small: number;
  };
}) {
  const titleFontSize = baseFontSizes.title * scale;
  const subtitleFontSize = baseFontSizes.subtitle * scale;
  const bodyFontSize = baseFontSizes.body * scale;
  const padding = Math.max(1, 2 * scale);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="bg-yellow-800 text-white rounded-t text-center"
        style={{ padding: `${padding}px` }}
      >
        <h3
          className="font-bold leading-tight"
          style={{ fontSize: `${titleFontSize}px` }}
        >
          {card.name}
        </h3>
        <div style={{ fontSize: `${subtitleFontSize}px` }}>
          奇迹 - {card.vp} VP
        </div>
      </div>

      {/* Illustration area */}
      {card.illustrationImage && (
        <div
          className="flex-1 relative rounded overflow-hidden"
          style={{ margin: `${padding}px 0` }}
        >
          <img
            src={card.illustrationImage.url}
            alt="Miracle illustration"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Details */}
      <div
        className="bg-white/90 rounded"
        style={{
          padding: `${padding}px`,
          fontSize: `${bodyFontSize}px`,
          lineHeight: 1.2,
        }}
      >
        <div style={{ marginBottom: `${padding / 2}px` }}>
          <span className="font-bold text-yellow-700">【条件】</span>
          <p
            className="leading-tight"
            style={{ marginTop: `${padding / 4}px` }}
          >
            {card.condition}
          </p>
        </div>
        {card.oneTimeBonus && (
          <div style={{ marginBottom: `${padding / 2}px` }}>
            <span className="font-bold text-green-700">【奖励】</span>
            <p
              className="leading-tight"
              style={{ marginTop: `${padding / 4}px` }}
            >
              {card.oneTimeBonus}
            </p>
          </div>
        )}
        {card.suitableRoute && (
          <div className="text-gray-600 italic">
            <p className="leading-tight">适合路线: {card.suitableRoute}</p>
          </div>
        )}
      </div>
    </div>
  );
}

function CulturalDecreeCardContent({
  card,
  scale,
  baseFontSizes,
}: {
  card: CulturalDecreeCard;
  scale: number;
  baseFontSizes: {
    title: number;
    subtitle: number;
    body: number;
    small: number;
  };
}) {
  const titleFontSize = baseFontSizes.title * scale;
  const subtitleFontSize = baseFontSizes.subtitle * scale;
  const bodyFontSize = baseFontSizes.body * scale;
  const padding = Math.max(1, 2 * scale);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="bg-blue-800 text-white rounded-t text-center"
        style={{ padding: `${padding}px` }}
      >
        <h3
          className="font-bold leading-tight"
          style={{ fontSize: `${titleFontSize}px` }}
        >
          {card.name}
        </h3>
        <div style={{ fontSize: `${subtitleFontSize}px` }}>
          文化法令 {card.type && `(${card.type})`}
        </div>
      </div>

      {/* Illustration area */}
      {card.illustrationImage && (
        <div
          className="flex-1 relative rounded overflow-hidden"
          style={{ margin: `${padding}px 0` }}
        >
          <img
            src={card.illustrationImage.url}
            alt="Cultural decree illustration"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {/* Effect */}
      <div
        className="bg-white/90 rounded"
        style={{
          padding: `${padding}px`,
          fontSize: `${bodyFontSize}px`,
          lineHeight: 1.2,
        }}
      >
        <div>
          <span className="font-bold text-blue-700">【效果】</span>
          <p
            className="leading-tight"
            style={{ marginTop: `${padding / 4}px` }}
          >
            {card.effect}
          </p>
        </div>
      </div>
    </div>
  );
}
