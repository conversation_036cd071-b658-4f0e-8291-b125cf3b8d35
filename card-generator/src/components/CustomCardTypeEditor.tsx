import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Save, X } from "lucide-react";
import type { CustomCardType, CustomField, CustomFieldType, LayoutMethod } from "../types/customCard";

interface CustomCardTypeEditorProps {
  cardType: CustomCardType;
  onChange: (cardType: CustomCardType) => void;
  onSave: (cardType: CustomCardType) => void;
  onCancel: () => void;
}

export function CustomCardTypeEditor({ cardType, onChange, onSave, onCancel }: CustomCardTypeEditorProps) {
  const [localCardType, setLocalCardType] = useState<CustomCardType>(cardType);

  const updateCardType = (updates: Partial<CustomCardType>) => {
    const updated = { ...localCardType, ...updates, updatedAt: Date.now() };
    setLocalCardType(updated);
    onChange(updated);
  };

  const updateDimensions = (field: keyof CustomCardType['dimensions'], value: number) => {
    updateCardType({
      dimensions: { ...localCardType.dimensions, [field]: value }
    });
  };

  const addField = () => {
    const newField: CustomField = {
      id: `field_${Date.now()}`,
      name: "New Field",
      type: "text",
      fieldPath: "newField",
      required: false,
      layoutMethod: "flexbox",
      flexOrder: localCardType.fields.length,
      textStyle: {
        fontSize: 14,
        fontFamily: "Arial",
        fontWeight: "normal",
        color: "#000000",
        textAlign: "left",
        lineHeight: 1.2,
        letterSpacing: 0
      }
    };

    updateCardType({
      fields: [...localCardType.fields, newField]
    });
  };

  const updateField = (fieldId: string, updates: Partial<CustomField>) => {
    const updatedFields = localCardType.fields.map(field =>
      field.id === fieldId ? { ...field, ...updates } : field
    );
    updateCardType({ fields: updatedFields });
  };

  const removeField = (fieldId: string) => {
    const updatedFields = localCardType.fields.filter(field => field.id !== fieldId);
    updateCardType({ fields: updatedFields });
  };

  const handleSave = () => {
    onSave(localCardType);
  };

  return (
    <div className="space-y-6">
      {/* Basic Properties */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Properties</CardTitle>
          <CardDescription>Configure the basic properties of your custom card type.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Card Type Name</Label>
              <Input
                id="name"
                value={localCardType.name}
                onChange={(e) => updateCardType({ name: e.target.value })}
                placeholder="Enter card type name"
              />
            </div>
            <div>
              <Label htmlFor="orientation">Orientation</Label>
              <Select
                value={localCardType.orientation}
                onValueChange={(value: "portrait" | "landscape") => updateCardType({ orientation: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="portrait">Portrait</SelectItem>
                  <SelectItem value="landscape">Landscape</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={localCardType.description || ""}
              onChange={(e) => updateCardType({ description: e.target.value })}
              placeholder="Optional description for this card type"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Dimensions */}
      <Card>
        <CardHeader>
          <CardTitle>Card Dimensions</CardTitle>
          <CardDescription>Set the physical dimensions and print properties.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="width">Width (mm)</Label>
              <Input
                id="width"
                type="number"
                value={localCardType.dimensions.width}
                onChange={(e) => updateDimensions('width', Number(e.target.value))}
                min="10"
                max="200"
              />
            </div>
            <div>
              <Label htmlFor="height">Height (mm)</Label>
              <Input
                id="height"
                type="number"
                value={localCardType.dimensions.height}
                onChange={(e) => updateDimensions('height', Number(e.target.value))}
                min="10"
                max="300"
              />
            </div>
            <div>
              <Label htmlFor="bleed">Bleed (mm)</Label>
              <Input
                id="bleed"
                type="number"
                value={localCardType.dimensions.bleed}
                onChange={(e) => updateDimensions('bleed', Number(e.target.value))}
                min="0"
                max="10"
                step="0.5"
              />
            </div>
            <div>
              <Label htmlFor="dpi">DPI</Label>
              <Select
                value={localCardType.dimensions.dpi.toString()}
                onValueChange={(value) => updateDimensions('dpi', Number(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="150">150 DPI</SelectItem>
                  <SelectItem value="300">300 DPI</SelectItem>
                  <SelectItem value="600">600 DPI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fields Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>Data Fields</span>
            <Button onClick={addField} size="sm">
              Add Field
            </Button>
          </CardTitle>
          <CardDescription>
            Define the data fields that will be available for this card type.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {localCardType.fields.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No fields defined yet. Add your first field to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {localCardType.fields.map((field, index) => (
                <FieldEditor
                  key={field.id}
                  field={field}
                  index={index}
                  onUpdate={(updates) => updateField(field.id, updates)}
                  onRemove={() => removeField(field.id)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={handleSave}>
          <Save className="w-4 h-4 mr-2" />
          Save
        </Button>
      </div>
    </div>
  );
}

interface FieldEditorProps {
  field: CustomField;
  index: number;
  onUpdate: (updates: Partial<CustomField>) => void;
  onRemove: () => void;
}

function FieldEditor({ field, index, onUpdate, onRemove }: FieldEditorProps) {
  return (
    <Card>
      <CardContent className="pt-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label>Field Name</Label>
            <Input
              value={field.name}
              onChange={(e) => onUpdate({ name: e.target.value })}
              placeholder="Field name"
            />
          </div>
          <div>
            <Label>Field Path</Label>
            <Input
              value={field.fieldPath}
              onChange={(e) => onUpdate({ fieldPath: e.target.value })}
              placeholder="e.g., name, stats.attack"
            />
          </div>
          <div>
            <Label>Type</Label>
            <Select
              value={field.type}
              onValueChange={(value: CustomFieldType) => onUpdate({ type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Text</SelectItem>
                <SelectItem value="image">Image</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={field.required}
                onChange={(e) => onUpdate({ required: e.target.checked })}
              />
              Required
            </label>
          </div>
          <Button variant="destructive" size="sm" onClick={onRemove}>
            Remove
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
