import { useState, useEffect } from 'react'
import { Link } from '@tanstack/react-router'
import { useDeckStore } from '../hooks/useDeckStore'
import { cardForgeDB } from '../utils/database'

interface DeckInfo {
  id: string
  name: string
  cardCount: number
  updatedAt: number
}

export function DecksManager() {
  const { deck, loadDeck, createDeck } = useDeckStore()
  const [allDecks, setAllDecks] = useState<DeckInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [newDeckName, setNewDeckName] = useState('')

  useEffect(() => {
    loadAllDecks()
  }, [])

  const loadAllDecks = async () => {
    try {
      setLoading(true)
      const decks = await cardForgeDB.getAllDecks()
      setAllDecks(decks)
    } catch (error) {
      console.error('Failed to load decks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateDeck = async () => {
    if (!newDeckName.trim()) return
    
    try {
      await createDeck(newDeckName.trim())
      setNewDeckName('')
      await loadAllDecks()
    } catch (error) {
      console.error('Failed to create deck:', error)
    }
  }

  const handleLoadDeck = async (deckId: string) => {
    try {
      const deckData = await cardForgeDB.loadDeck(deckId)
      if (deckData) {
        await loadDeck(deckData)
      }
    } catch (error) {
      console.error('Failed to load deck:', error)
    }
  }

  const handleDeleteDeck = async (deckId: string, deckName: string) => {
    if (!confirm(`Are you sure you want to delete "${deckName}"? This cannot be undone.`)) {
      return
    }

    try {
      await cardForgeDB.deleteDeck(deckId)
      await loadAllDecks()
      
      // If we deleted the currently loaded deck, clear it
      if (deck?.id === deckId) {
        // Load the first available deck or create a new one
        const updatedDecks = await cardForgeDB.getAllDecks()
        if (updatedDecks.length > 0) {
          const firstDeck = await cardForgeDB.loadDeck(updatedDecks[0].id)
          if (firstDeck) {
            await loadDeck(firstDeck)
          }
        } else {
          await createDeck('New Deck')
        }
      }
    } catch (error) {
      console.error('Failed to delete deck:', error)
    }
  }

  const handleExportDatabase = async () => {
    try {
      const data = await cardForgeDB.exportDatabase()
      const blob = new Blob([data], { type: 'application/octet-stream' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `cardforge-backup-${new Date().toISOString().split('T')[0]}.db`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export database:', error)
    }
  }

  const handleImportDatabase = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const arrayBuffer = await file.arrayBuffer()
      const data = new Uint8Array(arrayBuffer)
      await cardForgeDB.importDatabase(data)
      await loadAllDecks()
      
      // Load the first deck from imported data
      const decks = await cardForgeDB.getAllDecks()
      if (decks.length > 0) {
        const firstDeck = await cardForgeDB.loadDeck(decks[0].id)
        if (firstDeck) {
          await loadDeck(firstDeck)
        }
      }
    } catch (error) {
      console.error('Failed to import database:', error)
      alert('Failed to import database. Please check the file format.')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading decks...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Deck Manager</h1>
          
          {/* Create New Deck */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">Create New Deck</h2>
            <div className="flex gap-4">
              <input
                type="text"
                value={newDeckName}
                onChange={(e) => setNewDeckName(e.target.value)}
                placeholder="Enter deck name..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleCreateDeck()}
              />
              <button
                onClick={handleCreateDeck}
                disabled={!newDeckName.trim()}
                className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
              >
                Create Deck
              </button>
            </div>
          </div>

          {/* Database Actions */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">Database</h2>
            <div className="flex gap-4">
              <button
                onClick={handleExportDatabase}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                Export All Data
              </button>
              <label className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 cursor-pointer">
                Import Data
                <input
                  type="file"
                  accept=".db"
                  onChange={handleImportDatabase}
                  className="hidden"
                />
              </label>
            </div>
          </div>
        </div>

        {/* Decks List */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Your Decks ({allDecks.length})</h2>
          
          {allDecks.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No decks found</p>
              <p className="text-gray-400">Create your first deck to get started!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {allDecks.map((deckInfo) => (
                <div
                  key={deckInfo.id}
                  className={`p-4 border rounded-lg hover:shadow-md transition-shadow ${
                    deck?.id === deckInfo.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="font-semibold text-lg truncate">{deckInfo.name}</h3>
                    {deck?.id === deckInfo.id && (
                      <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                        Current
                      </span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-4">
                    <p>{deckInfo.cardCount} cards</p>
                    <p>Updated {new Date(deckInfo.updatedAt).toLocaleDateString()}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleLoadDeck(deckInfo.id)}
                      className="flex-1 bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600"
                    >
                      Load
                    </button>
                    <Link
                      to="/deck"
                      onClick={() => handleLoadDeck(deckInfo.id)}
                      className="flex-1 bg-purple-500 text-white px-3 py-2 rounded text-sm hover:bg-purple-600 text-center"
                    >
                      Manage
                    </Link>
                    <Link
                      to="/editor"
                      onClick={() => handleLoadDeck(deckInfo.id)}
                      className="flex-1 bg-green-500 text-white px-3 py-2 rounded text-sm hover:bg-green-600 text-center"
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteDeck(deckInfo.id, deckInfo.name)}
                      className="bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}