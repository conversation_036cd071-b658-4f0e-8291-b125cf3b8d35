// Craft.js components for the card designer
// Note: This is a placeholder implementation until @craftjs/core is installed

import React from 'react';

// Placeholder for useNode hook
const useNode = () => ({
  connectors: {
    connect: (ref: any) => ref,
    drag: (ref: any) => ref,
  },
  actions: {
    setProp: (callback: any) => {},
  },
  selected: false,
  hovered: false,
});

// Text Component for cards
export const CraftText = ({ text = "Sample Text", fontSize = 14, color = "#000000", fontWeight = "normal" }: {
  text?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: string;
}) => {
  const { connectors: { connect, drag }, actions: { setProp } } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        fontSize: `${fontSize}px`,
        color,
        fontWeight,
        padding: '4px',
        cursor: 'pointer',
        border: '1px dashed transparent',
      }}
      onDoubleClick={() => {
        const newText = prompt('Enter new text:', text);
        if (newText !== null) {
          setProp((props: any) => props.text = newText);
        }
      }}
    >
      {text}
    </div>
  );
};

// Image Component for cards
export const CraftImage = ({ src = "", alt = "Image", width = 100, height = 100 }: {
  src?: string;
  alt?: string;
  width?: number;
  height?: number;
}) => {
  const { connectors: { connect, drag }, actions: { setProp } } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        border: '1px dashed #ccc',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        backgroundColor: '#f5f5f5',
      }}
      onDoubleClick={() => {
        const newSrc = prompt('Enter image URL:', src);
        if (newSrc !== null) {
          setProp((props: any) => props.src = newSrc);
        }
      }}
    >
      {src ? (
        <img src={src} alt={alt} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
      ) : (
        <span style={{ fontSize: '12px', color: '#666' }}>📷 Image</span>
      )}
    </div>
  );
};

// Container Component for layout
export const CraftContainer = ({ children, padding = 8, backgroundColor = "transparent", flexDirection = "column" }: {
  children?: React.ReactNode;
  padding?: number;
  backgroundColor?: string;
  flexDirection?: "row" | "column";
}) => {
  const { connectors: { connect, drag } } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        padding: `${padding}px`,
        backgroundColor,
        display: 'flex',
        flexDirection,
        gap: '8px',
        minHeight: '50px',
        border: '1px dashed #ddd',
        cursor: 'pointer',
      }}
    >
      {children}
    </div>
  );
};

// Card Field Component - binds to data fields
export const CraftCardField = ({ fieldId, fieldType = "text", placeholder = "Field" }: {
  fieldId?: string;
  fieldType?: "text" | "image";
  placeholder?: string;
}) => {
  const { connectors: { connect, drag } } = useNode();

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        padding: '8px',
        border: '2px dashed #3b82f6',
        borderRadius: '4px',
        backgroundColor: '#eff6ff',
        cursor: 'pointer',
        minHeight: '30px',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <span style={{ fontSize: '12px', color: '#1d4ed8', fontWeight: 'medium' }}>
        {fieldType === 'image' ? '📷' : '📝'} {placeholder}
        {fieldId && <span style={{ opacity: 0.7 }}> ({fieldId})</span>}
      </span>
    </div>
  );
};

// Craft.js component configurations (placeholders)
CraftText.craft = {
  props: {
    text: "Sample Text",
    fontSize: 14,
    color: "#000000",
    fontWeight: "normal",
  },
  related: {
    // Settings panel would go here
  },
};

CraftImage.craft = {
  props: {
    src: "",
    alt: "Image",
    width: 100,
    height: 100,
  },
  related: {
    // Settings panel would go here
  },
};

CraftContainer.craft = {
  props: {
    padding: 8,
    backgroundColor: "transparent",
    flexDirection: "column",
  },
  related: {
    // Settings panel would go here
  },
};

CraftCardField.craft = {
  props: {
    fieldId: "",
    fieldType: "text",
    placeholder: "Field",
  },
  related: {
    // Settings panel would go here
  },
};

// Toolbox component for dragging components
export const CraftToolbox = () => {
  return (
    <div className="p-4 bg-gray-50 border-r">
      <h3 className="font-medium mb-4">Components</h3>
      <div className="space-y-2">
        <div className="p-2 bg-white border rounded cursor-pointer hover:bg-gray-50">
          📝 Text
        </div>
        <div className="p-2 bg-white border rounded cursor-pointer hover:bg-gray-50">
          📷 Image
        </div>
        <div className="p-2 bg-white border rounded cursor-pointer hover:bg-gray-50">
          📦 Container
        </div>
        <div className="p-2 bg-white border rounded cursor-pointer hover:bg-gray-50">
          🏷️ Card Field
        </div>
      </div>
    </div>
  );
};

// Settings panel for selected component
export const CraftSettingsPanel = () => {
  return (
    <div className="p-4 bg-gray-50 border-l">
      <h3 className="font-medium mb-4">Properties</h3>
      <div className="text-sm text-gray-600">
        Select a component to edit its properties
      </div>
    </div>
  );
};

// Main editor wrapper (placeholder for Craft.js Editor)
export const CraftEditor = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="h-full flex">
      <CraftToolbox />
      <div className="flex-1 p-4 bg-white">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[400px]">
          {children}
        </div>
      </div>
      <CraftSettingsPanel />
    </div>
  );
};

// Frame component (placeholder for Craft.js Frame)
export const CraftFrame = ({ children }: { children: React.ReactNode }) => {
  return <div className="w-full h-full">{children}</div>;
};
