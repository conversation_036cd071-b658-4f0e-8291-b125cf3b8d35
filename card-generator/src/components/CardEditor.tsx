import { useState, useEffect } from 'react'
import { useDeckStore } from '../hooks/useDeckStore'
import { <PERSON><PERSON><PERSON>er } from './CardRenderer'
import { CARD_DIMENSIONS } from '../types/card'
import type { Card, CardAsset } from '../types/card'

export function CardEditor() {
  const {
    deck,
    selectedCardId,
    createDeck,
    addCard,
    updateCard,
    selectCard,
    deleteCard,
    duplicateCard,
    undo,
    redo,
    canUndo,
    canRedo
  } = useDeckStore()

  const [showBleed, setShowBleed] = useState(false)

  const selectedCard = deck?.cards.find(c => c.id === selectedCardId)

  useEffect(() => {
    if (!deck) {
      createDeck('My Deck')
    }
  }, [deck, createDeck])

  useEffect(() => {
    if (deck && deck.cards.length > 0 && !selectedCardId) {
      selectCard(deck.cards[0].id)
    }
  }, [deck, selectedCardId, selectCard])

  const handleCardUpdate = (field: keyof Card, value: any) => {
    if (!selectedCardId) return
    updateCard(selectedCardId, { [field]: value })
  }

  const handleAssetUpdate = (field: keyof Card, url: string, fallback?: string) => {
    if (!selectedCardId) return
    const asset: CardAsset = { url, fallback }
    updateCard(selectedCardId, { [field]: asset })
  }

  const handleStatsUpdate = (stats: Record<string, number>) => {
    if (!selectedCardId) return
    updateCard(selectedCardId, { stats })
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'z':
          e.preventDefault()
          if (e.shiftKey) {
            redo()
          } else {
            undo()
          }
          break
        case 'n':
          e.preventDefault()
          addCard()
          break
        case 'd':
          e.preventDefault()
          if (selectedCardId) {
            duplicateCard(selectedCardId)
          }
          break
        case 'Backspace':
          e.preventDefault()
          if (selectedCardId && deck && deck.cards.length > 1) {
            deleteCard(selectedCardId)
          }
          break
      }
    }
  }

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [selectedCardId, deck, undo, redo, addCard, duplicateCard, deleteCard])

  if (!deck) return <div>Loading...</div>

  const dimensions = CARD_DIMENSIONS[deck.settings.cardSize]
  const effectiveDimensions = deck.settings.orientation === 'landscape' 
    ? { ...dimensions, width: dimensions.height, height: dimensions.width }
    : dimensions

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar - Card List */}
      <div className="w-80 bg-white border-r border-gray-300 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-2">{deck.settings.name}</h2>
          <div className="flex gap-2">
            <button
              onClick={addCard}
              className="flex-1 bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600 text-sm"
            >
              Add Card
            </button>
            <button
              onClick={undo}
              disabled={!canUndo}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50 text-sm"
            >
              Undo
            </button>
            <button
              onClick={redo}
              disabled={!canRedo}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50 text-sm"
            >
              Redo
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {deck.cards.map((card, index) => (
            <div
              key={card.id}
              className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedCardId === card.id ? 'bg-blue-50 border-blue-200' : ''
              }`}
              onClick={() => selectCard(card.id)}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{card.title || 'Untitled Card'}</h4>
                  <p className="text-xs text-gray-500">Card {index + 1}</p>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      duplicateCard(card.id)
                    }}
                    className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
                  >
                    Copy
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      if (deck.cards.length > 1) {
                        deleteCard(card.id)
                      }
                    }}
                    disabled={deck.cards.length <= 1}
                    className="text-xs px-2 py-1 bg-red-200 text-red-700 rounded hover:bg-red-300 disabled:opacity-50"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Card Preview */}
        <div className="flex-1 p-8 flex items-center justify-center">
          {selectedCard && (
            <div className="relative">
              <CardRenderer
                card={selectedCard}
                dimensions={effectiveDimensions}
                scale={2}
                showBleed={showBleed}
                className="shadow-lg"
              />
              <div className="absolute -bottom-8 left-0 right-0 flex justify-center">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={showBleed}
                    onChange={(e) => setShowBleed(e.target.checked)}
                  />
                  Show bleed
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Properties Panel */}
        <div className="w-80 bg-white border-l border-gray-300 p-4 overflow-y-auto">
          {selectedCard && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Card Properties</h3>

              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                  type="text"
                  value={selectedCard.title || ''}
                  onChange={(e) => handleCardUpdate('title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Subtitle</label>
                <input
                  type="text"
                  value={selectedCard.subtitle || ''}
                  onChange={(e) => handleCardUpdate('subtitle', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Cost</label>
                <input
                  type="number"
                  value={selectedCard.cost || ''}
                  onChange={(e) => handleCardUpdate('cost', e.target.value ? Number(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  value={selectedCard.description || ''}
                  onChange={(e) => handleCardUpdate('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Background Image URL</label>
                <input
                  type="url"
                  value={selectedCard.backgroundImage?.url || ''}
                  onChange={(e) => handleAssetUpdate('backgroundImage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Illustration Image URL</label>
                <input
                  type="url"
                  value={selectedCard.illustrationImage?.url || ''}
                  onChange={(e) => handleAssetUpdate('illustrationImage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Rarity Icon URL</label>
                <input
                  type="url"
                  value={selectedCard.rarityIcon?.url || ''}
                  onChange={(e) => handleAssetUpdate('rarityIcon', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Stats</label>
                <div className="space-y-2">
                  {Object.entries(selectedCard.stats || {}).map(([key, value]) => (
                    <div key={key} className="flex gap-2">
                      <input
                        type="text"
                        value={key}
                        onChange={(e) => {
                          const newStats = { ...selectedCard.stats }
                          delete newStats[key]
                          newStats[e.target.value] = value
                          handleStatsUpdate(newStats)
                        }}
                        className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                        placeholder="Stat name"
                      />
                      <input
                        type="number"
                        value={value}
                        onChange={(e) => {
                          const newStats = { ...selectedCard.stats }
                          newStats[key] = Number(e.target.value)
                          handleStatsUpdate(newStats)
                        }}
                        className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                      <button
                        onClick={() => {
                          const newStats = { ...selectedCard.stats }
                          delete newStats[key]
                          handleStatsUpdate(newStats)
                        }}
                        className="px-2 py-1 bg-red-200 text-red-700 rounded text-sm hover:bg-red-300"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={() => {
                      const newStats = { ...selectedCard.stats, 'New Stat': 0 }
                      handleStatsUpdate(newStats)
                    }}
                    className="w-full px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm"
                  >
                    Add Stat
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}