import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { useDeckStore } from '../hooks/useDeckStore'
import { CardRenderer } from './CardRenderer'
import { GameCardRenderer } from './GameCardRenderer'
import { CARD_DIMENSIONS } from '../types/card'
import type { Card, CardSize, CardOrientation } from '../types/card'

export function DeckOverview() {
  const {
    deck,
    updateDeckSettings,
    addCard,
    deleteCard,
    duplicateCard,
    reorderCards,
    selectCard,
    saveDeck,
    loadDeck,
    createDeck
  } = useDeckStore()

  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)

  if (!deck) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">No deck loaded</h2>
          <Link
            to="/"
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go to Home
          </Link>
        </div>
      </div>
    )
  }

  const dimensions = CARD_DIMENSIONS[deck.settings.cardSize]
  
  const getEffectiveDimensions = (card: Card) => {
    const cardOrientation = card.orientation || 'portrait'
    return cardOrientation === 'landscape' 
      ? { ...dimensions, width: dimensions.height, height: dimensions.width }
      : dimensions
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    setDragOverIndex(index)
  }

  const handleDragEnd = () => {
    if (draggedIndex !== null && dragOverIndex !== null && draggedIndex !== dragOverIndex) {
      reorderCards(draggedIndex, dragOverIndex)
    }
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const handleLoadDeck = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const deckData = JSON.parse(e.target?.result as string)
        loadDeck(deckData).catch(console.error)
      } catch (error) {
        alert('Failed to load deck file. Please check the file format.')
      }
    }
    reader.readAsText(file)
  }

  const handleSaveDeck = () => {
    const deckJson = saveDeck()
    const blob = new Blob([deckJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${deck.settings.name.replace(/\s+/g, '_')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportPDF = async () => {
    if (!deck || deck.cards.length === 0) {
      alert('No cards to export')
      return
    }

    try {
      // Dynamic import to avoid SSR issues
      const { PDFExporter } = await import('../utils/pdfExport')
      const exporter = new PDFExporter(deck)
      
      // Show progress notification
      const cardCount = deck.cards.length
      console.log(`Starting PDF export for ${cardCount} cards...`)
      
      await exporter.downloadPDF()
      
      // Success notification
      alert(`Successfully exported ${cardCount} individual card PDFs!`)
    } catch (error) {
      console.error('PDF export failed:', error)
      alert('Failed to export PDF. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{deck.settings.name}</h1>
              <p className="text-gray-600">{deck.cards.length} cards</p>
            </div>
            <div className="flex gap-3">
              <Link
                to="/editor"
                className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
              >
                Edit Cards
              </Link>
              <button
                onClick={handleSaveDeck}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                Save Deck
              </button>
              <button
                onClick={handleExportPDF}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                Export PDF
              </button>
            </div>
          </div>

          {/* Deck Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Deck Name</label>
              <input
                type="text"
                value={deck.settings.name}
                onChange={(e) => updateDeckSettings({ name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Card Size</label>
              <select
                value={deck.settings.cardSize}
                onChange={(e) => updateDeckSettings({ cardSize: e.target.value as CardSize })}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="poker">Poker (63×88mm)</option>
                <option value="tarot">Tarot (70×120mm)</option>
                <option value="mini">Mini (44×67mm)</option>
                <option value="square">Square (70×70mm)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">DPI</label>
              <select
                value={deck.settings.dpi}
                onChange={(e) => updateDeckSettings({ dpi: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={300}>300 DPI</option>
                <option value={600}>600 DPI</option>
                <option value={1200}>1200 DPI</option>
              </select>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex gap-4 flex-wrap">
            <button
              onClick={() => addCard('faction_gray')}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Add New Card
            </button>
            <label className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 cursor-pointer">
              Load Deck
              <input
                type="file"
                accept=".json"
                onChange={handleLoadDeck}
                className="hidden"
              />
            </label>
            <button
              onClick={() => createDeck('New Deck').catch(console.error)}
              className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
            >
              New Deck
            </button>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Cards ({deck.cards.length})</h2>
          
          {deck.cards.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No cards in this deck yet</p>
              <button
                onClick={() => addCard('faction_gray')}
                className="bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600"
              >
                Add Your First Card
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {deck.cards.map((card, index) => (
                <div
                  key={card.id}
                  className={`relative group cursor-pointer ${
                    draggedIndex === index ? 'opacity-50' : ''
                  } ${
                    dragOverIndex === index ? 'ring-2 ring-blue-500' : ''
                  }`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragEnd={handleDragEnd}
                  onClick={() => {
                    selectCard(card.id)
                  }}
                >
                  {/* Use GameCardRenderer for new card types, fallback to CardRenderer for legacy */}
                  {'category' in card ? (
                    <GameCardRenderer
                      card={card}
                      dimensions={getEffectiveDimensions(card)}
                      scale={0.8}
                      className="hover:shadow-lg transition-shadow"
                    />
                  ) : (
                    <CardRenderer
                      card={card as any}
                      dimensions={getEffectiveDimensions(card)}
                      scale={0.8}
                      className="hover:shadow-lg transition-shadow"
                    />
                  )}
                  
                  {/* Card Info Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex gap-2">
                        <Link
                          to="/editor"
                          onClick={(e) => {
                            e.stopPropagation()
                            selectCard(card.id)
                          }}
                          className="bg-purple-500 text-white px-2 py-1 rounded text-xs hover:bg-purple-600"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            duplicateCard(card.id)
                          }}
                          className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
                        >
                          Copy
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            if (deck.cards.length > 1) {
                              deleteCard(card.id)
                            }
                          }}
                          disabled={deck.cards.length <= 1}
                          className="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 disabled:opacity-50"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Card Number */}
                  <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    {index + 1}
                  </div>
                  
                  {/* Card Title */}
                  <div className="mt-2 text-center">
                    <p className="text-sm font-medium truncate">
                      {'name' in card ? card.name : (card as any).title || 'Untitled Card'}
                    </p>
                    <p className="text-xs text-gray-500">
                      Updated {new Date(card.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}