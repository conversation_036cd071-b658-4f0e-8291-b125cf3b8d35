import { useState } from 'react'
import type { Card, CardDimensions } from '../types/card'

interface CardRendererProps {
  card: Card
  dimensions: CardDimensions
  scale?: number
  showBleed?: boolean
  className?: string
}

interface ImageLoadState {
  [key: string]: 'loading' | 'loaded' | 'error'
}

export function CardRenderer({ 
  card, 
  dimensions, 
  scale = 1, 
  showBleed = false,
  className = ''
}: CardRendererProps) {
  const [imageStates, setImageStates] = useState<ImageLoadState>({})

  const handleImageLoad = (key: string) => {
    setImageStates(prev => ({ ...prev, [key]: 'loaded' }))
  }

  const handleImageError = (key: string) => {
    setImageStates(prev => ({ ...prev, [key]: 'error' }))
  }

  const handleImageStart = (key: string) => {
    setImageStates(prev => ({ ...prev, [key]: 'loading' }))
  }

  const actualWidth = showBleed ? dimensions.width + (dimensions.bleed * 2) : dimensions.width
  const actualHeight = showBleed ? dimensions.height + (dimensions.bleed * 2) : dimensions.height

  const cardStyle = {
    width: `${actualWidth * scale}px`,
    height: `${actualHeight * scale}px`,
    fontSize: `${12 * scale}px`
  }

  const isPortrait = dimensions.height > dimensions.width

  return (
    <div 
      className={`relative bg-white border border-gray-300 overflow-hidden ${className}`}
      style={cardStyle}
    >
      {/* Background Image */}
      {card.backgroundImage && (
        <div className="absolute inset-0">
          <img
            src={card.backgroundImage.url}
            alt="Card background"
            className="w-full h-full object-cover"
            onLoad={() => handleImageLoad('background')}
            onError={() => handleImageError('background')}
            onLoadStart={() => handleImageStart('background')}
          />
          {imageStates.background === 'error' && card.backgroundImage.fallback && (
            <img
              src={card.backgroundImage.fallback}
              alt="Card background fallback"
              className="w-full h-full object-cover"
              onLoad={() => handleImageLoad('background-fallback')}
              onError={() => handleImageError('background-fallback')}
            />
          )}
          {imageStates.background === 'error' && !card.backgroundImage.fallback && (
            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
              <span className="text-gray-500 text-xs">Background image failed to load</span>
            </div>
          )}
        </div>
      )}

      {/* Main content overlay */}
      <div className="absolute inset-0 p-2 flex flex-col" style={{ padding: `${4 * scale}px` }}>
        {/* Header section with title and cost */}
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            {card.title && (
              <h3 className="font-bold text-black bg-white/80 px-1 rounded text-sm leading-tight">
                {card.title}
              </h3>
            )}
            {card.subtitle && (
              <p className="text-xs text-gray-700 bg-white/70 px-1 rounded mt-1">
                {card.subtitle}
              </p>
            )}
          </div>
          {card.cost !== undefined && (
            <div className="bg-yellow-400 text-black font-bold rounded-full w-8 h-8 flex items-center justify-center text-sm ml-2">
              {card.cost}
            </div>
          )}
        </div>

        {/* Illustration area */}
        {card.illustrationImage && (
          <div className="flex-1 relative mb-2 min-h-0">
            <img
              src={card.illustrationImage.url}
              alt="Card illustration"
              className="w-full h-full object-cover rounded border border-gray-200"
              onLoad={() => handleImageLoad('illustration')}
              onError={() => handleImageError('illustration')}
              onLoadStart={() => handleImageStart('illustration')}
            />
            {imageStates.illustration === 'error' && card.illustrationImage.fallback && (
              <img
                src={card.illustrationImage.fallback}
                alt="Card illustration fallback"
                className="w-full h-full object-cover rounded border border-gray-200"
                onLoad={() => handleImageLoad('illustration-fallback')}
                onError={() => handleImageError('illustration-fallback')}
              />
            )}
            {imageStates.illustration === 'error' && !card.illustrationImage.fallback && (
              <div className="w-full h-full bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-xs">No image</span>
              </div>
            )}
          </div>
        )}

        {/* Stats section */}
        {card.stats && Object.keys(card.stats).length > 0 && (
          <div className="flex gap-1 mb-2">
            {Object.entries(card.stats).map(([key, value]) => (
              <div key={key} className="bg-red-500 text-white text-xs px-2 py-1 rounded">
                {key}: {value}
              </div>
            ))}
          </div>
        )}

        {/* Description text */}
        {card.description && (
          <div className="bg-white/90 p-2 rounded text-xs leading-relaxed">
            {card.description}
          </div>
        )}

        {/* Rarity icon */}
        {card.rarityIcon && (
          <div className="absolute top-2 right-2">
            <img
              src={card.rarityIcon.url}
              alt="Rarity"
              className="w-4 h-4"
              onLoad={() => handleImageLoad('rarity')}
              onError={() => handleImageError('rarity')}
              onLoadStart={() => handleImageStart('rarity')}
            />
            {imageStates.rarity === 'error' && card.rarityIcon.fallback && (
              <img
                src={card.rarityIcon.fallback}
                alt="Rarity fallback"
                className="w-4 h-4"
                onLoad={() => handleImageLoad('rarity-fallback')}
                onError={() => handleImageError('rarity-fallback')}
              />
            )}
          </div>
        )}
      </div>

      {/* Bleed indicators */}
      {showBleed && (
        <>
          <div className="absolute inset-0 border-2 border-red-500 pointer-events-none opacity-50" />
          <div 
            className="absolute border-2 border-blue-500 pointer-events-none opacity-50"
            style={{
              left: `${dimensions.bleed * scale}px`,
              top: `${dimensions.bleed * scale}px`,
              right: `${dimensions.bleed * scale}px`,
              bottom: `${dimensions.bleed * scale}px`
            }}
          />
        </>
      )}
    </div>
  )
}