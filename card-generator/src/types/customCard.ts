export type LayoutMethod = "flexbox" | "absolute";

export type CustomFieldType = "image" | "text";

export interface CustomCardDimensions {
  width: number;
  height: number;
  bleed: number;
  dpi: number;
}

export interface TextFieldStyle {
  fontSize: number;
  fontFamily: string;
  fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900";
  color: string;
  textAlign: "left" | "center" | "right" | "justify";
  lineHeight: number;
  letterSpacing: number;
}

export interface ImageFieldStyle {
  objectFit: "cover" | "contain" | "fill" | "scale-down" | "none";
  borderRadius: number;
  opacity: number;
}

export interface PositionStyle {
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
}

export interface FlexboxStyle {
  flexDirection: "row" | "column" | "row-reverse" | "column-reverse";
  justifyContent: "flex-start" | "flex-end" | "center" | "space-between" | "space-around" | "space-evenly";
  alignItems: "flex-start" | "flex-end" | "center" | "stretch" | "baseline";
  flexWrap: "nowrap" | "wrap" | "wrap-reverse";
  gap: number;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface CustomField {
  id: string;
  name: string;
  type: CustomFieldType;
  fieldPath: string; // JSON path to bind data (e.g., "name", "stats.attack", "description")
  required: boolean;
  
  // Styling based on field type
  textStyle?: TextFieldStyle;
  imageStyle?: ImageFieldStyle;
  
  // Layout positioning
  layoutMethod: LayoutMethod;
  position?: PositionStyle; // For absolute positioning
  flexOrder?: number; // For flexbox ordering
  
  // Default values
  defaultValue?: string | number;
  placeholder?: string;
}

export interface CustomCardElement {
  id: string;
  name: string;
  type: "container" | "field";
  
  // For containers
  children?: string[]; // IDs of child elements
  layoutMethod?: LayoutMethod;
  flexboxStyle?: FlexboxStyle;
  
  // For fields
  fieldId?: string; // Reference to CustomField
  
  // Common positioning
  position?: PositionStyle;
  
  // Styling
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  boxShadow?: string;
}

export interface CustomCardType {
  id: string;
  name: string;
  description?: string;
  
  // Card physical properties
  dimensions: CustomCardDimensions;
  orientation: "portrait" | "landscape";
  
  // Design structure
  fields: CustomField[];
  elements: CustomCardElement[];
  rootElementId: string; // ID of the root container element
  
  // Metadata
  createdAt: number;
  updatedAt: number;
  version: number;
}

export interface CustomCardData {
  [fieldPath: string]: any; // Dynamic data based on field paths
}

export interface CustomCard {
  id: string;
  name: string;
  category: "custom";
  customCardTypeId: string;
  data: CustomCardData;
  
  // Standard card properties
  illustrationImage?: import('./card').CardAsset;
  backImage?: import('./card').CardAsset;
  orientation?: import('./card').CardOrientation;
  createdAt: number;
  updatedAt: number;
}

// Template for creating new custom card types
export const DEFAULT_CUSTOM_CARD_TYPE: Omit<CustomCardType, 'id' | 'createdAt' | 'updatedAt'> = {
  name: "New Card Type",
  description: "",
  dimensions: {
    width: 63, // Standard poker card width in mm
    height: 88, // Standard poker card height in mm
    bleed: 3,
    dpi: 300
  },
  orientation: "portrait",
  fields: [],
  elements: [{
    id: "root",
    name: "Root Container",
    type: "container",
    children: [],
    layoutMethod: "flexbox",
    flexboxStyle: {
      flexDirection: "column",
      justifyContent: "flex-start",
      alignItems: "stretch",
      flexWrap: "nowrap",
      gap: 0,
      padding: { top: 8, right: 8, bottom: 8, left: 8 }
    },
    position: {
      x: 0,
      y: 0,
      width: 100, // Percentage
      height: 100, // Percentage
      zIndex: 0
    },
    backgroundColor: "#ffffff",
    borderRadius: 8
  }],
  rootElementId: "root",
  version: 1
};
