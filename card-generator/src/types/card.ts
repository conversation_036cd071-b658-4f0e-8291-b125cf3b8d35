export type CardOrientation = "portrait" | "landscape";

export type CardSize = "poker" | "tarot" | "mini" | "square";

export type CardCategory =
  | "god"
  | "faction"
  | "event"
  | "miracle"
  | "cultural_decree";

export type FactionColor = "red" | "yellow" | "green" | "purple" | "gray";

export type CardAge = "extra" | "age1" | "age2" | "age3";

export interface CardDimensions {
  width: number;
  height: number;
  bleed: number;
}

export interface CardAsset {
  url: string;
  fallback?: string;
}

export interface CardCost {
  gold?: number;
  culture?: number;
  faith?: number;
  special?: string; // For special costs like "X (minimum 3)"
}

// Card back images by type
export interface CardBackImages {
  god?: CardAsset;
  faction?: CardAsset;
  event?: CardAsset;
  miracle?: CardAsset;
  cultural_decree?: CardAsset;
}

// Base card interface
export interface BaseCard {
  id: string;
  name: string;
  category: CardCategory;
  illustrationImage?: CardAsset;
  backImage?: CardAsset;
  orientation?: CardOrientation;
  createdAt: number;
  updatedAt: number;
}

// God cards
export interface GodCard extends BaseCard {
  category: "god";
  passive: string;
  divinePower: string;
  faithCost: number | string;
}

// Faction cards
export interface FactionCard extends BaseCard {
  category: "faction";
  faction: FactionColor;
  age: CardAge;
  quantity?: number;
  cost: CardCost | string;
  leaderEffect?: string;
  permanentEffect?: string;
  militaryPower?: number;
}

// Event cards
export interface EventCard extends BaseCard {
  category: "event";
  effect: string;
  note?: string;
}

// Miracle cards
export interface MiracleCard extends BaseCard {
  category: "miracle";
  condition: string;
  vp: number;
  oneTimeBonus?: string;
  suitableRoute?: string;
}

// Cultural Decree cards
export interface CulturalDecreeCard extends BaseCard {
  category: "cultural_decree";
  effect: string;
  type?: "basic" | "advanced";
}

// Union type for all card types
export type Card =
  | GodCard
  | FactionCard
  | EventCard
  | MiracleCard
  | CulturalDecreeCard;

// Legacy interface for backward compatibility
export interface LegacyCard {
  id: string;
  title?: string;
  subtitle?: string;
  cost?: number;
  stats?: Record<string, number>;
  description?: string;
  backgroundImage?: CardAsset;
  illustrationImage?: CardAsset;
  rarityIcon?: CardAsset;
  orientation?: CardOrientation;
  createdAt: number;
  updatedAt: number;
}

export interface DeckSettings {
  name: string;
  cardSize: CardSize;
  bleedWidth: number;
  dpi: number;
  defaultFont: string;
}

export interface Deck {
  id: string;
  settings: DeckSettings;
  cards: Card[];
  backImages: CardBackImages;
  createdAt: number;
  updatedAt: number;
}

// For backward compatibility with legacy decks
export interface LegacyDeck {
  id: string;
  settings: DeckSettings;
  cards: LegacyCard[];
  createdAt: number;
  updatedAt: number;
}

export const CARD_DIMENSIONS: Record<CardSize, CardDimensions> = {
  poker: { width: 63, height: 88, bleed: 2 },
  tarot: { width: 70, height: 120, bleed: 2 },
  mini: { width: 44, height: 67, bleed: 2 },
  square: { width: 70, height: 70, bleed: 2 },
};

export const FACTION_COLORS: Record<
  FactionColor,
  { name: string; bg: string; text: string; border: string }
> = {
  red: {
    name: "红色 (军事)",
    bg: "bg-red-500",
    text: "text-white",
    border: "border-red-600",
  },
  yellow: {
    name: "黄色 (经济)",
    bg: "bg-yellow-500",
    text: "text-black",
    border: "border-yellow-600",
  },
  green: {
    name: "绿色 (文化)",
    bg: "bg-green-500",
    text: "text-white",
    border: "border-green-600",
  },
  purple: {
    name: "紫色 (信仰)",
    bg: "bg-purple-500",
    text: "text-white",
    border: "border-purple-600",
  },
  gray: {
    name: "灰色 (建筑)",
    bg: "bg-gray-500",
    text: "text-white",
    border: "border-gray-600",
  },
};

export const CARD_CATEGORIES: Record<
  CardCategory,
  { name: string; description: string }
> = {
  god: { name: "神祇卡", description: "强大的神祇，提供被动能力和神力" },
  faction: { name: "派系卡", description: "领袖和建筑卡，构成你的文明" },
  event: { name: "事件卡", description: "影响所有玩家的随机事件" },
  miracle: { name: "奇迹卡", description: "达成特定条件获得的胜利点数" },
  cultural_decree: { name: "文化法令", description: "文化发展带来的长期收益" },
};

export const DEFAULT_DECK_SETTINGS: DeckSettings = {
  name: "New Deck",
  cardSize: "poker",
  bleedWidth: 2,
  dpi: 300,
  defaultFont: "Arial",
};
