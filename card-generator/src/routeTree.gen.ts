/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as EditorRouteImport } from './routes/editor'
import { Route as DecksRouteImport } from './routes/decks'
import { Route as DeckRouteImport } from './routes/deck'
import { Route as CustomTypesRouteImport } from './routes/custom-types'
import { Route as IndexRouteImport } from './routes/index'
import { Route as CustomTypesIdEditRouteImport } from './routes/custom-types.$id.edit'

const EditorRoute = EditorRouteImport.update({
  id: '/editor',
  path: '/editor',
  getParentRoute: () => rootRouteImport,
} as any)
const DecksRoute = DecksRouteImport.update({
  id: '/decks',
  path: '/decks',
  getParentRoute: () => rootRouteImport,
} as any)
const DeckRoute = DeckRouteImport.update({
  id: '/deck',
  path: '/deck',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomTypesRoute = CustomTypesRouteImport.update({
  id: '/custom-types',
  path: '/custom-types',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomTypesIdEditRoute = CustomTypesIdEditRouteImport.update({
  id: '/$id/edit',
  path: '/$id/edit',
  getParentRoute: () => CustomTypesRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/custom-types': typeof CustomTypesRouteWithChildren
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/custom-types/$id/edit': typeof CustomTypesIdEditRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/custom-types': typeof CustomTypesRouteWithChildren
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/custom-types/$id/edit': typeof CustomTypesIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/custom-types': typeof CustomTypesRouteWithChildren
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/custom-types/$id/edit': typeof CustomTypesIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/custom-types'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/custom-types/$id/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/custom-types'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/custom-types/$id/edit'
  id:
    | '__root__'
    | '/'
    | '/custom-types'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/custom-types/$id/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CustomTypesRoute: typeof CustomTypesRouteWithChildren
  DeckRoute: typeof DeckRoute
  DecksRoute: typeof DecksRoute
  EditorRoute: typeof EditorRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/editor': {
      id: '/editor'
      path: '/editor'
      fullPath: '/editor'
      preLoaderRoute: typeof EditorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/decks': {
      id: '/decks'
      path: '/decks'
      fullPath: '/decks'
      preLoaderRoute: typeof DecksRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deck': {
      id: '/deck'
      path: '/deck'
      fullPath: '/deck'
      preLoaderRoute: typeof DeckRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/custom-types': {
      id: '/custom-types'
      path: '/custom-types'
      fullPath: '/custom-types'
      preLoaderRoute: typeof CustomTypesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/custom-types/$id/edit': {
      id: '/custom-types/$id/edit'
      path: '/$id/edit'
      fullPath: '/custom-types/$id/edit'
      preLoaderRoute: typeof CustomTypesIdEditRouteImport
      parentRoute: typeof CustomTypesRoute
    }
  }
}

interface CustomTypesRouteChildren {
  CustomTypesIdEditRoute: typeof CustomTypesIdEditRoute
}

const CustomTypesRouteChildren: CustomTypesRouteChildren = {
  CustomTypesIdEditRoute: CustomTypesIdEditRoute,
}

const CustomTypesRouteWithChildren = CustomTypesRoute._addFileChildren(
  CustomTypesRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CustomTypesRoute: CustomTypesRouteWithChildren,
  DeckRoute: DeckRoute,
  DecksRoute: DecksRoute,
  EditorRoute: EditorRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
