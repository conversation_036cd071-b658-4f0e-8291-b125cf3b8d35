/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as EditorRouteImport } from './routes/editor'
import { Route as DecksRouteImport } from './routes/decks'
import { Route as DeckRouteImport } from './routes/deck'
import { Route as IndexRouteImport } from './routes/index'

const EditorRoute = EditorRouteImport.update({
  id: '/editor',
  path: '/editor',
  getParentRoute: () => rootRouteImport,
} as any)
const DecksRoute = DecksRouteImport.update({
  id: '/decks',
  path: '/decks',
  getParentRoute: () => rootRouteImport,
} as any)
const DeckRoute = DeckRouteImport.update({
  id: '/deck',
  path: '/deck',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/deck' | '/decks' | '/editor'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/deck' | '/decks' | '/editor'
  id: '__root__' | '/' | '/deck' | '/decks' | '/editor'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DeckRoute: typeof DeckRoute
  DecksRoute: typeof DecksRoute
  EditorRoute: typeof EditorRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/editor': {
      id: '/editor'
      path: '/editor'
      fullPath: '/editor'
      preLoaderRoute: typeof EditorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/decks': {
      id: '/decks'
      path: '/decks'
      fullPath: '/decks'
      preLoaderRoute: typeof DecksRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deck': {
      id: '/deck'
      path: '/deck'
      fullPath: '/deck'
      preLoaderRoute: typeof DeckRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DeckRoute: DeckRoute,
  DecksRoute: DecksRoute,
  EditorRoute: EditorRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
