import { useState, useCallback, useEffect } from "react";
import type {
  Deck,
  Card,
  DeckSettings,
  LegacyDeck,
  LegacyCard,
  CardBackImages,
  CardCategory,
} from "../types/card";
import { DEFAULT_DECK_SETTINGS } from "../types/card";
import {
  createCardFromTemplate,
  createCustomCard,
} from "../utils/cardTemplates";
import { cardForgeDB } from "../utils/database";

interface DeckStore {
  deck: Deck | null;
  selectedCardId: string | null;
  history: Deck[];
  historyIndex: number;
  isLoading: boolean;

  createDeck: (name: string) => Promise<void>;
  loadDeck: (deckData: Deck | LegacyDeck) => Promise<void>;
  saveDeck: () => string;
  updateDeckSettings: (settings: Partial<DeckSettings>) => void;
  updateBackImages: (backImages: Partial<CardBackImages>) => void;

  addCard: (templateKey?: string) => void;
  addCustomCard: (customCardTypeId: string, name?: string) => void;
  duplicateCard: (cardId: string) => void;
  deleteCard: (cardId: string) => void;
  updateCard: (cardId: string, updates: Partial<Card>) => void;
  reorderCards: (fromIndex: number, toIndex: number) => void;
  selectCard: (cardId: string | null) => void;

  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function createEmptyCard(templateKey?: string): Card {
  if (templateKey) {
    return createCardFromTemplate(templateKey);
  }

  // Default to faction card template for backward compatibility
  return createCardFromTemplate("faction_gray", {
    name: "New Card",
  });
}

function convertLegacyCard(legacyCard: LegacyCard): Card {
  const now = Date.now();
  return {
    id: legacyCard.id,
    name: legacyCard.title || "Untitled Card",
    category: "faction",
    faction: "gray",
    age: "age1",
    cost: legacyCard.cost ? { gold: legacyCard.cost } : { gold: 0 },
    leaderEffect: legacyCard.description,
    illustrationImage: legacyCard.illustrationImage,
    backImage: legacyCard.backgroundImage,
    orientation: legacyCard.orientation,
    createdAt: legacyCard.createdAt,
    updatedAt: now,
  };
}

function convertLegacyDeck(legacyDeck: LegacyDeck): Deck {
  return {
    id: legacyDeck.id,
    settings: legacyDeck.settings,
    cards: legacyDeck.cards.map(convertLegacyCard),
    backImages: {},
    createdAt: legacyDeck.createdAt,
    updatedAt: Date.now(),
  };
}

function createEmptyDeck(name: string): Deck {
  const now = Date.now();
  return {
    id: generateId(),
    settings: { ...DEFAULT_DECK_SETTINGS, name },
    cards: [],
    backImages: {},
    createdAt: now,
    updatedAt: now,
  };
}

export function useDeckStore(): DeckStore {
  const [deck, setDeck] = useState<Deck | null>(null);
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [history, setHistory] = useState<Deck[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(true);

  const addToHistory = useCallback(
    (newDeck: Deck) => {
      setHistory((prev) => {
        const newHistory = prev.slice(0, historyIndex + 1);
        newHistory.push({ ...newDeck });
        return newHistory.slice(-50); // Keep last 50 states
      });
      setHistoryIndex((prev) => Math.min(prev + 1, 49));
    },
    [historyIndex]
  );

  const updateDeck = useCallback(
    (updater: (prev: Deck) => Deck) => {
      setDeck((prev) => {
        if (!prev) return null;
        const newDeck = updater(prev);
        newDeck.updatedAt = Date.now();
        addToHistory(newDeck);

        // Save to database asynchronously
        cardForgeDB.saveDeck(newDeck).catch((error) => {
          console.error("Failed to save deck to database:", error);
        });

        return newDeck;
      });
    },
    [addToHistory]
  );

  const createDeck = useCallback(async (name: string) => {
    const newDeck = createEmptyDeck(name);
    setDeck(newDeck);
    setSelectedCardId(null);
    setHistory([{ ...newDeck }]);
    setHistoryIndex(0);

    // Save to database
    try {
      await cardForgeDB.saveDeck(newDeck);
    } catch (error) {
      console.error("Failed to save new deck to database:", error);
    }
  }, []);

  const loadDeck = useCallback(async (deckData: Deck | LegacyDeck) => {
    // Check if it's a legacy deck and convert if necessary
    let convertedDeck: Deck;
    if (
      "cards" in deckData &&
      deckData.cards.length > 0 &&
      "title" in deckData.cards[0]
    ) {
      // Legacy deck detected
      convertedDeck = convertLegacyDeck(deckData as LegacyDeck);
    } else {
      convertedDeck = deckData as Deck;
    }

    setDeck(convertedDeck);
    setSelectedCardId(null);
    setHistory([{ ...convertedDeck }]);
    setHistoryIndex(0);

    // Save to database
    try {
      await cardForgeDB.saveDeck(convertedDeck);
    } catch (error) {
      console.error("Failed to save loaded deck to database:", error);
    }
  }, []);

  const saveDeck = useCallback(() => {
    if (!deck) return "";
    return JSON.stringify(deck, null, 2);
  }, [deck]);

  const updateDeckSettings = useCallback(
    (settings: Partial<DeckSettings>) => {
      updateDeck((prev) => ({
        ...prev,
        settings: { ...prev.settings, ...settings },
      }));
    },
    [updateDeck]
  );

  const updateBackImages = useCallback(
    (backImages: Partial<CardBackImages>) => {
      updateDeck((prev) => ({
        ...prev,
        backImages: { ...prev.backImages, ...backImages },
      }));
    },
    [updateDeck]
  );

  const addCard = useCallback(
    (templateKey?: string) => {
      const newCard = createEmptyCard(templateKey);
      updateDeck((prev) => ({
        ...prev,
        cards: [...prev.cards, newCard],
      }));
      setSelectedCardId(newCard.id);
    },
    [updateDeck]
  );

  const addCustomCard = useCallback(
    (customCardTypeId: string, name?: string) => {
      const newCard = createCustomCard({
        name: name || "New Custom Card",
        customCardTypeId,
        data: {},
      });
      updateDeck((prev) => ({
        ...prev,
        cards: [...prev.cards, newCard],
      }));
      setSelectedCardId(newCard.id);
    },
    [updateDeck]
  );

  const duplicateCard = useCallback(
    (cardId: string) => {
      updateDeck((prev) => {
        const cardIndex = prev.cards.findIndex((c) => c.id === cardId);
        if (cardIndex === -1) return prev;

        const originalCard = prev.cards[cardIndex];
        const newCard = {
          ...originalCard,
          id: generateId(),
          name: `${originalCard.name} (Copy)`,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };

        const newCards = [...prev.cards];
        newCards.splice(cardIndex + 1, 0, newCard);

        return { ...prev, cards: newCards };
      });
    },
    [updateDeck]
  );

  const deleteCard = useCallback(
    (cardId: string) => {
      updateDeck((prev) => ({
        ...prev,
        cards: prev.cards.filter((c) => c.id !== cardId),
      }));
      if (selectedCardId === cardId) {
        setSelectedCardId(null);
      }
    },
    [updateDeck, selectedCardId]
  );

  const updateCard = useCallback(
    (cardId: string, updates: Partial<Card>) => {
      updateDeck((prev) => ({
        ...prev,
        cards: prev.cards.map((card) =>
          card.id === cardId
            ? ({ ...card, ...updates, updatedAt: Date.now() } as Card)
            : card
        ),
      }));
    },
    [updateDeck]
  );

  const reorderCards = useCallback(
    (fromIndex: number, toIndex: number) => {
      updateDeck((prev) => {
        const newCards = [...prev.cards];
        const [removed] = newCards.splice(fromIndex, 1);
        newCards.splice(toIndex, 0, removed);
        return { ...prev, cards: newCards };
      });
    },
    [updateDeck]
  );

  const selectCard = useCallback((cardId: string | null) => {
    setSelectedCardId(cardId);
  }, []);

  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const prevDeck = history[historyIndex - 1];
      setDeck({ ...prevDeck });
      setHistoryIndex((prev) => prev - 1);
    }
  }, [history, historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const nextDeck = history[historyIndex + 1];
      setDeck({ ...nextDeck });
      setHistoryIndex((prev) => prev + 1);
    }
  }, [history, historyIndex]);

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Load from database on mount
  useEffect(() => {
    const loadFromDatabase = async () => {
      try {
        setIsLoading(true);

        // Try to load the last used deck from database
        const lastDeckId = await cardForgeDB.getLastUsedDeck();
        if (lastDeckId) {
          const deckData = await cardForgeDB.loadDeck(lastDeckId);
          if (deckData) {
            setDeck(deckData);
            setSelectedCardId(null);
            setHistory([{ ...deckData }]);
            setHistoryIndex(0);
            setIsLoading(false);
            return;
          }
        }

        // Fallback to localStorage for migration
        const saved = localStorage.getItem("cardforge-deck");
        if (saved) {
          try {
            const deckData = JSON.parse(saved) as Deck | LegacyDeck;
            await loadDeck(deckData);
            // Remove from localStorage after successful migration
            localStorage.removeItem("cardforge-deck");
            setIsLoading(false);
            return;
          } catch (e) {
            console.warn("Failed to load saved deck:", e);
          }
        }

        // No existing deck found, we're ready for user to create one
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to load from database:", error);
        setIsLoading(false);
      }
    };

    loadFromDatabase();
  }, []);

  return {
    deck,
    selectedCardId,
    history,
    historyIndex,
    isLoading,
    createDeck,
    loadDeck,
    saveDeck,
    updateDeckSettings,
    updateBackImages,
    addCard,
    addCustomCard,
    duplicateCard,
    deleteCard,
    updateCard,
    reorderCards,
    selectCard,
    undo,
    redo,
    canUndo,
    canRedo,
  };
}
